<template>
  <div>
    <!-- 表格 -->
    <a-table
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      :data="tableData"
      :row-selection="rowSelection"
      :scroll="{ x: '100%', y: '100%' }"
      v-model:selectedKeys="localSelectedKeys"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
    >
      <template #columns>
        <a-table-column title="序号" :width="80" align="center">
          <template #cell="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="房号" data-index="roomNumber" :width="100" />
        <a-table-column title="项目" data-index="projectName" :width="150" />
        <a-table-column title="所属地块" data-index="plotName" :width="120" />
        <a-table-column title="楼栋" data-index="buildingName" :width="100" />
        <a-table-column title="房源" data-index="roomName" :width="120" />
        <a-table-column title="用途" data-index="usage" :width="100" />
        <a-table-column title="运营房间名称" data-index="operationRoomName" :width="150" />
        <a-table-column title="运营房间ID" data-index="operationRoomId" :width="120" />
        <a-table-column title="绑定日期" data-index="bindDate" :width="120" />
        <a-table-column title="操作" :width="120" align="center" fixed="right">
          <template #cell="{ record }">
            <a-space>
              <a-button type="text" size="small" @click="handleEdit(record)">
                解绑
              </a-button>
              <a-button type="text" size="small" @click="handleDetail(record)">
                详情
              </a-button>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';

// Props
interface Props {
  filterForm: {
    projectId: string;
    buildingOrRoom: string;
  };
  selectedKeys: string[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:selectedKeys': [value: string[]];
  'update:loading': [value: boolean];
  'edit-device': [record: any];
  'view-detail': [record: any];
}>();

// 响应式数据
const loading = ref(false);
const tableData = ref<any[]>([]);

// 内部选中的行keys
const localSelectedKeys = ref<string[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
});

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
})

// 模拟数据
const mockData = [
  {
    id: '1',
    roomNumber: '1',
    projectName: '浙江（平阳）万洋众创城',
    plotName: 'F05地块',
    buildingName: '50幢',
    roomName: '101',
    usage: '宿舍',
    operationRoomName: 'F05-50幢-101',
    operationRoomId: '23',
    bindDate: '2015-05-04'
  },
  {
    id: '2',
    roomNumber: '2',
    projectName: '浙江（平阳）万洋众创城',
    plotName: 'F05地块',
    buildingName: '50幢',
    roomName: '102',
    usage: '宿舍',
    operationRoomName: 'F05-50幢-102',
    operationRoomId: '24',
    bindDate: '2015-05-03'
  },
  {
    id: '3',
    roomNumber: '3',
    projectName: '浙江（平阳）万洋众创城',
    plotName: 'F05地块',
    buildingName: '50幢',
    roomName: '103',
    usage: '宿舍',
    operationRoomName: 'F05-50幢-103',
    operationRoomId: '25',
    bindDate: '2015-05-02'
  },
  {
    id: '4',
    roomNumber: '4',
    projectName: '浙江（平阳）万洋众创城',
    plotName: 'F05地块',
    buildingName: '50幢',
    roomName: '104',
    usage: '商铺',
    operationRoomName: 'F05-50幢-104',
    operationRoomId: '26',
    bindDate: '2015-05-03'
  },
  {
    id: '5',
    roomNumber: '5',
    projectName: '浙江（平阳）万洋众创城',
    plotName: 'F05地块',
    buildingName: '50幢',
    roomName: '105',
    usage: '商铺',
    operationRoomName: 'F05-50幢-105',
    operationRoomId: '27',
    bindDate: '2015-05-02'
  }
];

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  emit('update:loading', true);
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 根据筛选条件过滤数据
    let filteredData = [...mockData];
    
    if (props.filterForm.buildingOrRoom) {
      filteredData = filteredData.filter(item => 
        item.buildingName.includes(props.filterForm.buildingOrRoom) ||
        item.roomName.includes(props.filterForm.buildingOrRoom) ||
        item.operationRoomName.includes(props.filterForm.buildingOrRoom)
      );
    }
    
    // 分页处理
    const start = (pagination.current - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    tableData.value = filteredData.slice(start, end);
    pagination.total = filteredData.length;
    
  } catch (error) {
    console.error('获取数据失败:', error);
    Message.error('获取数据失败');
  } finally {
    loading.value = false;
    emit('update:loading', false);
  }
};

// 分页变化
const onPageChange = (page: number) => {
  pagination.current = page;
  fetchTableData();
};

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchTableData();
};

// 编辑处理
const handleEdit = (record: any) => {
  emit('edit-device', record);
};

// 详情处理
const handleDetail = (record: any) => {
  emit('view-detail', record);
};

// 监听selectedKeys的变化，确保添加防循环检查
watch(() => props.selectedKeys, (newVal) => {
  // 检查是否与当前值相同，如果相同则不更新
  if (JSON.stringify(localSelectedKeys.value) !== JSON.stringify(newVal)) {
    localSelectedKeys.value = [...newVal]
  }
}, { deep: true })

// 监听本地selectedKeys的变化，同步到父组件，添加防循环检查
watch(() => localSelectedKeys.value, (newVal) => {
  // 检查是否与props中的值相同，如果相同则不触发更新
  if (JSON.stringify(props.selectedKeys) !== JSON.stringify(newVal)) {
    emit('update:selectedKeys', newVal)
  }
}, { deep: true })

// 暴露给父组件的方法
defineExpose({
  fetchTableData,
  resetPagination: () => {
    pagination.current = 1
  }
})

// 组件挂载时不自动获取数据，等待父组件调用
onMounted(() => {
  // 不自动调用 fetchTableData，由父组件控制
})
</script>

<style scoped>
/* 样式可以根据需要添加 */
</style>
