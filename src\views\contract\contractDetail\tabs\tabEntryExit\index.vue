<template>
    <div class="contract-entry-exit">
        <section>
            <sectionTitle title="进场信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="false"
                :scroll="{ x: 1100 }"
                :bordered="{ cell: true }"
            ></a-table>
        </a-card>
        <section>
            <sectionTitle title="出场信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table
                :columns="columns2"
                :data="tableData2"
                :pagination="false"
                :scroll="{ x: 1200 }"
                :bordered="{ cell: true }"
            ></a-table>
        </a-card>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { getDictLabel } from '@/dict';

    const tableData = ref<any[]>([]);

    const columns = computed(() => {
        return [
            {
                title: '序号',
                dataIndex: 'index',
                width: 70,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '合同编号',
                dataIndex: 'contractNo',
                width: 160,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '合同用途',
                dataIndex: 'contractPurpose',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: any }) => {
                    return getDictLabel(
                        'diversification_purpose',
                        record.contractPurpose?.toString()
                    );
                },
            },
            {
                title: '承租方',
                dataIndex: 'tenantName',
                width: 140,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '租期',
                dataIndex: 'entryTime',
                width: 180,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: any }) => {
                    return record.rentStartDate && record.rentEndDate
                        ? `${record.rentStartDate} 至 ${record.rentEndDate}`
                        : '';
                },
            },
            {
                title: '未进场房源',
                dataIndex: 'roomName',
                width: 220,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '未进场房源数',
                dataIndex: 'unenterNum',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '已进场房源',
                dataIndex: 'roomName',
                width: 220,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '已进场房源数',
                dataIndex: 'enteredRoomCount',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '操作',
                slotName: 'operations',
                width: 120,
                fixed: 'right',
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });
    const tableData2 = ref<any[]>([]);

    const columns2 = computed(() => {
        return [
            {
                title: '序号',
                dataIndex: 'index',
                width: 70,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '合同编号',
                dataIndex: 'contractNo',
                width: 160,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '合同用途',
                dataIndex: 'contractPurpose',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
                render: ({ record }: { record: any }) => {
                    return getDictLabel(
                        'diversification_purpose',
                        record.contractPurpose?.toString()
                    );
                },
            },
            {
                title: '承租方',
                dataIndex: 'tenantName',
                width: 140,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '退租类型',
                dataIndex: 'terminateType',
                slotName: 'terminateType',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '退租日期',
                dataIndex: 'exitDate',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '退租房源',
                dataIndex: 'exitHouses',
                width: 200,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '房源数',
                dataIndex: 'houseCount',
                width: 80,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '办理状态',
                dataIndex: 'houseCount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '办理进度',
                dataIndex: 'houseCount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '费用合计',
                dataIndex: 'houseCount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '办理流程',
                dataIndex: 'houseCount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '签字方式',
                dataIndex: 'houseCount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '操作',
                slotName: 'operations',
                width: 120,
                fixed: 'right',
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });
</script>

<style scoped lang="less"></style>
