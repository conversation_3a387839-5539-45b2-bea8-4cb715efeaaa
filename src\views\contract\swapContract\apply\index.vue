<template>
    <div class="expand-apply">
        <section>
            <sectionTitle title="基本信息" />
            <a-card :bordered="false" :body-style="{ padding: '16px 32px' }">
                <a-form :model="contractData" :rules="rules" :wrapper-col-props="{ span: 16 }" auto-label-width>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="contractNo" label="合同编号">
                                <a-input v-model="contractData.contractNo" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="contractPeriod" label="合同周期">
                                <a-input v-model="contractPeriod" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="signDate" label="签订时间">
                                <a-input v-model="contractData.signDate" disabled />
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="contractPurposeLabel" label="合同用途">
                                <a-input v-model="contractPurposeLabel" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="ourSigningParty" label="承租方名称">
                                <a-input v-model="contractData.ourSigningParty" disabled />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="customerTypeLabel" label="承租类型">
                                <a-input v-model="customerTypeLabel" disabled />
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                    <a-grid :cols="3" :col-gap="16" :row-gap="0">
                        <a-grid-item>
                            <a-form-item field="changeDate" label="换房日期">
                                <a-date-picker v-model="formData.changeDate" style="width: 100%" placeholder="请选择日期"
                                :disabled-date="disabledChangeDate" format="YYYY-MM-DD" :disabled="props.isViewMode" />
                            </a-form-item>
                        </a-grid-item>
                        <a-grid-item>
                            <a-form-item field="remark" label="备注">
                                <a-input v-model="formData.remark" placeholder="请输入" :disabled="props.isViewMode"/>
                            </a-form-item>
                        </a-grid-item>
                    </a-grid>
                </a-form>
            </a-card>
        </section>
        <section>
            <sectionTitle title="换房房源选择" />
            <a-card :bordered="false" :body-style="{ padding: '16px 0 0' }">
                <a-table :data="contractData.rooms" :columns="columns" :bordered="{ cell: true }" rowKey="roomId"
                    :row-selection="props.isViewMode ? undefined : rowSelection" v-model:selectedKeys="selectedKeys"></a-table>
            </a-card>
        </section>
        <section>
            <sectionTitle title="换房新房源选择" />
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <div class="content">
                    <a-space>
                        <a-input v-model="roomNames" readonly @click="handleOpenRoomDrawer"
                            placeholder="请选择房源" />
                        <a-button type="primary" @click="handleOpenRoomDrawer" :disabled="props.isViewMode">选择房源</a-button>
                    </a-space>
                    <div class="table-content">
                        <a-table :data="tableData" :columns="newColumns" :bordered="{ cell: true }"
                            :pagination="{ showJumper: true }" :scroll="{ x: 1200 }">
                            <template #discount="{ record }">
                                <a-input-number v-model="record.discount" style="background-color: #fff;"
                                    @change="onDiscountChange(record, $event)" :disabled="props.isViewMode">
                                    <template #suffix>%</template>
                                </a-input-number>
                            </template>
                            <template #signedUnitPrice="{ record }">
                                <a-input-number v-model="record.signedUnitPrice" style="background-color: #fff;" @change="onSignedUnitPriceChange(record, $event)" :disabled="props.isViewMode">
                                    <template #suffix>{{ unitMap.get(record.priceUnit) }}</template>
                                </a-input-number>
                            </template>
                            <template #signedMonthlyPrice="{ record }">
                                {{ formatAmount(record.signedMonthlyPrice) }}
                            </template>
                            <template #operations="{ record }">
                                <a-button type="text" size="mini" @click="removeRoom(record)" :disabled="props.isViewMode">移除</a-button>
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-card>
        </section>
    </div>
    <selectRoomsDrawer ref="selectRoomRef" @submit="getRoomList" />

</template>
<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue';
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import { useContractStore } from '@/store/modules/contract/index';
import { ContractAddDTO, ContractVo, getContractDetailBill, print, getContractTemplateList } from '@/api/contract';
import { getDictLabel } from '@/dict'
import { ContractRoomDTO } from '@/api/contract';
import { getOrderList } from '@/api/orderManagement'
import selectRoomsDrawer from '../../components/selectRoomsDrawer.vue'

// 定义props接收contractChangeDetail数据和查看模式
interface Props {
    contractChangeDetail?: any;
    isViewMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    contractChangeDetail: () => ({}),
    isViewMode: false
});

// 表单数据，支持双向绑定
const formData = ref({
    remark: '',
    changeDate: '' as string | any,
    changeRooms: [] as ContractRoomDTO[]
});

const customerTypeLabel = computed(() => {
    return  contractData.value.customer.customerType === 1 ? '个人' : '企业'
})

// 禁用变更执行日期的函数
const disabledChangeDate = (date: Date) => {
    const currentDate = dayjs(date)
    const startDate = contractData.value.startDate
    const endDate = contractData.value.endDate
    
    // 如果合同开始日期和结束日期都存在，则限制在这个范围内
    // 换房日期必须晚于合同开始日期，早于合同结束日期（不包含开始和结束日期）
    if (startDate && endDate) {
        return !currentDate.isAfter(dayjs(startDate), 'day') || !currentDate.isBefore(dayjs(endDate), 'day')
    }
    
    // 如果都没有，则不限制
    return false
}

// 只在初始化时监听props变化，更新本地表单数据
watch(() => props.contractChangeDetail, (newVal) => {
    if (newVal) {
        formData.value = {
            remark: newVal.remark || '',
            changeDate: newVal.changeDate ? dayjs(newVal.changeDate) : '',
            changeRooms: newVal.changeRooms || []
        };
        // 使用nextTick确保tableData已经初始化
        nextTick(() => {
            if (newVal.changeRooms && newVal.changeRooms.length > 0) {
                // 根据type字段区分数据
                const newRooms = newVal.changeRooms.filter((item: any) => item.type === 1);
                const exchangeRooms = newVal.changeRooms.filter((item: any) => item.type === 2);
                
                // 新房源显示在tableData中
                tableData.value = [...newRooms];
                
                // 换房房源更新到contractData.rooms中，并设置选中状态
                if (exchangeRooms.length > 0) {
                    contractData.value.rooms = contractData.value.rooms.map((item: any) => {
                        const exchangeRoom = exchangeRooms.find((room: any) => room.roomId === item.roomId);
                        if (exchangeRoom) {
                            return {
                                ...item,
                                isChecked: exchangeRoom.isChecked,
                                type: 2
                            };
                        }
                        return {
                            ...item,
                            isChecked: 0,
                            type: 2
                        };
                    });
                    
                    // 更新选中状态
                    selectedKeys.value = exchangeRooms
                        .filter((item: any) => item.isChecked === 1)
                        .map((item: any) => item.roomId);
                }
            }
        });
    }
}, { immediate: true, deep: false });

const contractStore = useContractStore();
const contractData = computed(() => contractStore.contractData as ContractAddDTO);

const contractPeriod = computed(() => {
    return contractData.value.startDate + ' - ' + contractData.value.endDate;
});

const contractPurposeLabel = computed(() => {
    return getDictLabel('diversification_purpose', Number(contractData.value.contractPurpose))
})

const roomNames = computed(() => {
    // 换房时显示新房源的名称
    const newRooms = formData.value.changeRooms?.filter(item => item.type === 1) || [];
    return newRooms.map(item => item.roomName).join(',');
})

const tableData = ref<ContractRoomDTO[]>([]);

const rules = ref({
    changeDate: [
        { required: true, message: '请选择换房日期' },
        {
            validator: (value: string, callback: (error?: string) => void) => {
                if (value) {
                    const changeDate = dayjs(value)
                    const startDate = contractData.value.startDate
                    const endDate = contractData.value.endDate
                    
                    if (startDate && endDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`换房日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`换房日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    } else if (startDate) {
                        if (!changeDate.isAfter(dayjs(startDate), 'day')) {
                            callback(`换房日期必须晚于合同开始日期（${startDate}）`)
                            return
                        }
                    } else if (endDate) {
                        if (!changeDate.isBefore(dayjs(endDate), 'day')) {
                            callback(`换房日期必须早于合同结束日期（${endDate}）`)
                            return
                        }
                    }
                }
                callback()
            }
        }
    ]
});

const columns = ref([
    {
        title: '房源名称',
        dataIndex: 'name',
        width: 300,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'name',
        render: ({ record }: { record: any }) => {
            return record.parcelName + '-' + record.buildingName + "-" + record.roomName
        },
    },
    {
        title: '租赁面积（㎡）',
        dataIndex: 'area',
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'area',
        width: 120
    },
]);

const selectedKeys = ref<string[]>([]);

const onExchangeSelectChange = (selectedRowKeys: string[], selectedRows: ContractRoomDTO[]) => {
  contractData.value.rooms = contractData.value.rooms.map((item: any) => ({
    ...item,
    isChecked: selectedRowKeys.includes(item.roomId) ? 1 : 0,
    type: 2
  }))
  selectedKeys.value = selectedRowKeys
}

// 表格多选
const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
    onChange: onExchangeSelectChange
}

const newColumns = ref([
    {
        title: '房源名称',
        dataIndex: 'roomName',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'roomName'
    },
    {
        title: '租赁面积（㎡）',
        dataIndex: 'area',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'area'
    },
    {
        title: '标准租金',
        dataIndex: 'standardUnitPrice',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'standardUnitPrice'
    },
    {
        title: '折扣',
        dataIndex: 'discount',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'discount'
    },
    {
        title: '签约单价',
        dataIndex: 'signedUnitPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedUnitPrice'
    },
    {
        title: '签约月总价（元/月）',
        dataIndex: 'signedMonthlyPrice',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'signedMonthlyPrice'
    },
    {
        title: '操作',
        dataIndex: 'operations',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        slotName: 'operations',
        fixed: 'right',
    },
]);

const isDiscountEditing = ref(false);
const isSignedUnitPriceEditing = ref(false);

// 更新签约单价
const updateSignedUnitPrice = (room: ContractRoomDTO) => {
    if (isSignedUnitPriceEditing.value) return; // 避免循环依赖
    isDiscountEditing.value = true;
    // 保留8位小数
    room.signedUnitPrice = Number(((room.standardUnitPrice ?? 0) * (room.discount / 100)).toFixed(8));
    room.signedMonthlyPrice = (room.signedUnitPrice ?? 0) * room.area;
    isDiscountEditing.value = false;
    // 同步更新formData，需要包含所有房源
    let originRooms = getOriginRooms()
    formData.value.changeRooms = [...tableData.value, ...originRooms];
};

// 更新折扣
const updateDiscount = (room: ContractRoomDTO) => {
    if (isDiscountEditing.value) return; // 避免循环依赖
    isSignedUnitPriceEditing.value = true;
    if (room.standardUnitPrice) {
        room.discount = Number(((room.signedUnitPrice / room.standardUnitPrice) * 100).toFixed(2));
    } else {
        room.discount = 100; // 标准租金为0时，默认折扣为100%
    }
    room.signedMonthlyPrice = (room.signedUnitPrice ?? 0) * room.area;
    isSignedUnitPriceEditing.value = false;
    // 同步更新formData，需要包含所有房源
    let originRooms = getOriginRooms()
    formData.value.changeRooms = [...tableData.value, ...originRooms];
};

// 折扣输入框变化
const onDiscountChange = (record: ContractRoomDTO, value: number) => {
    record.discount = value;
    updateSignedUnitPrice(record);
};

// 签约单价输入框变化
const onSignedUnitPriceChange = (record: ContractRoomDTO, value: number) => {
    record.signedUnitPrice = value;
    updateDiscount(record);
};

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 选择房源
const selectRoomRef = useTemplateRef('selectRoomRef')
const handleOpenRoomDrawer = () => {
    selectRoomRef.value?.openDrawer(contractData.value.rooms || [], formData.value.changeRooms || [])
}

const getOriginRooms = () => {
    let originRooms = contractData.value.rooms.map((item: any) => {
        return {
            ...item,
            isChecked: selectedKeys.value.includes(item.roomId) ? 1 : 0,
            type: 2 //类型: 1-新加, 2-去除
        }
    })
    return originRooms
}

const getRoomList = (data: ContractRoomDTO[]) => {
    console.log('获取到的房源数据:', data)
    // 初始化新选择的房源数据
    let tempData = data.map(room => {
        if(room.discount){
            room.signedUnitPrice = (room.standardUnitPrice ?? 0) * (room.discount / 100)
        }
        room.signedMonthlyPrice = (room.signedUnitPrice ?? 0) * room.area
        room.type = 1 
        return room
    })
    tableData.value = tempData
    
    // 更新formData中的changeRooms，包含新房源和原有房源
    let originRooms = getOriginRooms()
    formData.value.changeRooms = [...tempData, ...originRooms];
    loadValidOrders()
}

// 加载房间有效定单 - 只处理新房源的定单
const loadValidOrders = async () => {
    // 只获取新房源的定单，不包含原有合同的房源
    const newRoomIds = formData.value.changeRooms?.filter(item => {
        // 检查该房源是否在原有合同中存在
        const existingRoom = contractData.value.rooms.find(room => room.roomId === item.roomId);
        return !existingRoom; // 只返回不在原有合同中的房源
    }).map(item => item.roomId) || [];
    
    if (newRoomIds.length === 0) {
        // 如果没有新房源，清空定单
        contractData.value.bookings = [];
        contractData.value.bookingRelType = 1;
        return;
    }
    
    const { rows } = await getOrderList({
        pageNum: 1,
        pageSize: 10000,
        status: 2,
        roomIds: newRoomIds.join(','),
        projectId: contractStore.currentProjectId,
    })
    if (rows.length === 0) {
        contractData.value.bookings = []
        contractData.value.bookingRelType = 1
    } else {
        const bookings = rows.map((item: any) => {
            return {
                bookingId: item.id,
                bookedRoom: item.roomName,
                bookerName: item.customerName,
                bookingReceivedAmount: item.receivedAmount,
                bookingPaymentDate: item.receivedDate,
            }
        })

        if (contractStore.editType === 'toSign' && !!contractData.value.bookings && contractData.value.bookings.length > 0) {
            // 转签约需要判断房源获取的定单中是否包含转签约带过来的定单，如果包含，则类型为房间有效定单，否则为其他定单
            const isInclude = bookings.some((item: any) => item.bookingId === contractData.value.bookings?.[0]?.bookingId)
            if (isInclude) {
                contractData.value.bookingRelType = 0
                contractData.value.bookings = bookings
            } else {
                contractData.value.bookingRelType = 2
                contractData.value.bookings = contractData.value.bookings?.concat(bookings)
            }
        } else {
            contractData.value.bookingRelType = 0
            // 格式化定单列表
            contractData.value.bookings = bookings
        }
    }
}

// 移除房源
const removeRoom = (room: ContractRoomDTO) => {
    const index = tableData.value.findIndex(r => r.roomId === room.roomId);
    if (index > -1) {
        tableData.value.splice(index, 1);
        // 同步更新formData中的changeRooms，包含新房源和原有房源
        let originRooms = getOriginRooms()
        formData.value.changeRooms = [...tableData.value, ...originRooms];
    }
}

// 获取当前表单数据的方法，供父组件调用
const getFormData = () => {
    // 新房源（type: 1）已在 tableData 里
    const newRooms = tableData.value.map(item => ({
        ...item,
        type: 1,
        isChecked: 1
    }))
    
    // 换房房源（type: 2）从 contractData.rooms 中获取
    const exchangeRooms = contractData.value.rooms.map((item: any) => ({
        ...item,
        type: 2,
        isChecked: selectedKeys.value.includes(item.roomId) ? 1 : 0
    }))
    
    // 合并两类房源
    const allRooms = [
        ...exchangeRooms,
        ...newRooms
    ]
    
    return {
        ...props.contractChangeDetail,
        ...formData.value,
        changeDate: formData.value.changeDate ? dayjs(formData.value.changeDate).format('YYYY-MM-DD') : '',
        changeRooms: allRooms
    };
};

// 暴露方法给父组件
defineExpose({
    formData,
    tableData,
    getFormData
});
</script>
<style lang="less" scoped>
.table-content {
    margin-top: 16px;
}

.margin-top-16 {
    margin-top: 16px;
}
</style>