<template>
    <div class="container">
        <a-card class="general-card" :body-style="{ padding: 0 }">
            <!-- 合同类型 tab -->
            <a-tabs v-model:activeKey="activeContractType" size="large" hide-content @change="changeContractType">
                <template #extra>
                    <a-space style="padding-right: 16px;">
                        <a-button v-permission="['rent:contract:updateResponsible']" type="primary"
                            @click="updateResponsible">
                            <template #icon>
                                <icon-user />
                            </template>更新责任人</a-button>
                        <a-dropdown @select="handleAddContract">
                            <a-button type="primary" v-permission="['rent:contract:add']">
                                <template #icon>
                                    <icon-plus-circle />
                                </template>
                                新增合同
                                <icon-down />
                            </a-button>
                            <template #content>
                                <a-doption :value="0">非宿舍合同</a-doption>
                                <a-doption :value="1">宿舍合同</a-doption>
                                <a-doption :value="2">多经合同</a-doption>
                                <!-- <a-doption :value="3">日租房合同</a-doption> -->
                            </template>
                        </a-dropdown>
                        <a-button @click="exportData">导出</a-button>
                        <a-button @click="handleDownloadTemplate">导入模板</a-button>
                        <a-button @click="importData">合同导入</a-button>
                    </a-space>
                </template>
                <a-tab-pane v-for="item in contractTypeOptions" :key="item.value" :title="item.label" />
            </a-tabs>

            <a-card :bordered="false">
                <!-- 筛选器 -->
                <div class="search-form">
                    <a-row align="stretch">
                        <a-col :flex="1">
                            <a-form :model="searchForm" auto-label-width>
                                <a-grid :cols="3" :colGap="16" :collapsed="isCollapsed" :collapsed-rows="2">
                                    <a-grid-item>
                                        <a-form-item field="projectId" label="项目">
                                            <ProjectTreeSelect v-model="searchForm.projectId"
                                                @change="handleProjectChange" />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="roomName" label="租赁资源">
                                            <a-input v-model="searchForm.roomName" placeholder="请输入租赁资源" allow-clear />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="customerName" label="承租方">
                                            <a-input v-model="searchForm.customerName" placeholder="请输入承租方"
                                                allow-clear />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="contractPurposes" label="合同用途">
                                            <a-tree-select v-model="searchForm.contractPurposes" tree-checkable
                                                :tree-check-strictly="true" :data="contract_purpose"
                                                placeholder="请选择合同用途" allow-clear
                                                :fieldNames="{ title: 'label', key: 'value' }"></a-tree-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="ownerName" label="当前责任人">
                                            <a-input v-model="searchForm.ownerName" placeholder="请输入当前责任人"
                                                allow-clear />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="contractNo" label="合同号">
                                            <a-input v-model="searchForm.contractNo" placeholder="请输入合同号" allow-clear />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="operateTypes" label="最新操作类型">
                                            <a-select v-model="searchForm.operateTypes" placeholder="请选择最新操作类型" multiple
                                                allow-clear>
                                                <a-option v-for="item in contract_operate_type" :key="item.value"
                                                    :value="item.value">{{ item.label }}</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="statuses" label="合同状态">
                                            <a-select v-model="searchForm.statuses" placeholder="请选择合同状态" multiple
                                                allow-clear>
                                                <a-option v-for="item in contract_status" :key="item.value"
                                                    :value="item.value">{{
                                                        item.label }}</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="statusTwos" label="合同状态二级">
                                            <a-select v-model="searchForm.statusTwos" placeholder="请选择合同状态二级" multiple
                                                allow-clear>
                                                <a-option v-for="item in getDict('contract_status_detail')"
                                                    :key="item.value" :value="item.value"
                                                    :label="item.label"></a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    
                                    <a-grid-item>
                                        <a-form-item field="signType" label="签约类型">
                                            <a-select v-model="searchForm.signType" placeholder="请选择签约类型" allow-clear>
                                                <a-option v-for="item in contract_sign_type" :key="item.value"
                                                    :value="item.value">{{ item.label }}</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="signWays" label="签约方式">
                                            <a-select v-model="searchForm.signWays" placeholder="请选择签约方式" multiple
                                                allow-clear>
                                                <a-option v-for="item in contract_sign_way" :key="item.value"
                                                    :value="item.value">{{
                                                        item.label }}</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="signDate" label="签约日期">
                                            <a-range-picker v-model="searchForm.signDate" style="width: 100%" />
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="isUploadSignature" label="是否上传签署文件">
                                            <a-select v-model="searchForm.isUploadSignature" placeholder="请选择是否上传签署文件"
                                                allow-clear>
                                                <a-option :value="true">是</a-option>
                                                <a-option :value="false">否</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item field="isPaperConfirm" label="纸质文件是否确收">
                                            <a-select v-model="searchForm.isPaperConfirm" placeholder="请选择纸质文件是否确收"
                                                allow-clear>
                                                <a-option :value="true">是</a-option>
                                                <a-option :value="false">否</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                    <a-grid-item>
                                        <a-form-item v-if="activeContractStatus === 40" field="isFinish" label="是否完全结束">
                                            <a-select v-model="searchForm.isFinish" placeholder="请选择是否完全结束" allow-clear>
                                                <a-option :value="true">是</a-option>
                                                <a-option :value="false">否</a-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-grid-item>
                                </a-grid>
                            </a-form>
                        </a-col>
                        <a-divider style="margin-bottom: 16px;" direction="vertical" />
                        <a-col flex="86px">
                            <a-space direction="vertical">
                                <a-button type="primary" @click="handleSearch">
                                    <template #icon>
                                        <icon-search />
                                    </template>
                                    查询
                                </a-button>
                                <a-button @click="resetForm">
                                    <template #icon>
                                        <icon-refresh />
                                    </template>
                                    重置
                                </a-button>
                            </a-space>
                        </a-col>
                    </a-row>

                </div>
                <a-row>
                    <AdvancedSearch @toggle="(status: boolean) => { isCollapsed = !status }" />
                </a-row>
                <!-- 表头操作区 -->
                <div class="table-header">
                    <a-tabs v-model:activeKey="activeContractStatus" type="rounded" hide-content
                        @change="changeContractStatus">
                        <a-tab-pane v-for="item in contractStatusOptions" :key="item.value" :title="item.label" />

                        <template #extra>
                            <a-space v-if="activeContractStatus === 20">
                                <a-typography-text> 合同期未开始（{{ summaryData.contractPeriodNotStarted }}）
                                </a-typography-text>
                                <a-typography-text> 保证金未收齐（{{ summaryData.bondNotFullyReceived }}） </a-typography-text>
                                <a-typography-text> 合同期未开始&保证金未收齐（{{ summaryData.periodNotStartedAndBondNotReceived }}）
                                </a-typography-text>
                            </a-space>
                            <a-space v-if="activeContractStatus === 40">
                                <a-typography-text> 待退租（{{ summaryData.pendingTermination }}） </a-typography-text>
                                <a-typography-text> 退租待结算（{{ summaryData.normalTerminationPendingCheckout +
                                    summaryData.earlyTerminationPendingCheckout }}） </a-typography-text>
                                <a-typography-text> 正常退（{{ summaryData.normalTermination }}） </a-typography-text>
                                <a-typography-text> 提前退（{{ summaryData.earlyTermination }}） </a-typography-text>
                                <a-typography-text> 换房（{{ summaryData.roomChange }}） </a-typography-text>
                                <a-typography-text> 续签（{{ summaryData.renewal }}） </a-typography-text>
                                <a-typography-text> 作废（{{ summaryData.invalid }}） </a-typography-text>
                                <a-typography-text> 作废重签（{{ summaryData.invalidRenewal }}） </a-typography-text>
                            </a-space>
                        </template>
                    </a-tabs>
                </div>

                <!-- 数据表格 -->
                <a-table ref="tableRef" :columns="columns" :data="tableData" :pagination="pagination" column-resizable
                    :bordered="{ cell: true }" :scroll="{ x: 1 }" rowKey="id" :row-selection="rowSelection"
                    @selection-change="handleSelectionChange" @page-change="onPageChange"
                    @page-size-change="onPageSizeChange">
                    <!-- 操作列插槽 -->
                    <template #operations="{ record }">
                        <a-space>
                            <a-button v-permission="['rent:contract:detail']" type="text" size="mini"
                                @click="viewContract(record)">查看</a-button>
                            <a-button v-permission="['rent:contract:edit']" v-if="record.status === 10" type="text"
                                size="mini" @click="editContract(record)">编辑</a-button>
                            <a-button v-permission="['rent:contract:delete']" v-if="record.status === 10" type="text"
                                size="mini" @click="handleDeleteContract(record)">删除</a-button>
                            <a-button v-permission="['rent:contract:handle']"
                                v-if="[20, 30].includes(record.status) && record.approveStatus != 1" type="text"
                                size="mini" @click="handleCollect(record)">收款码</a-button>
                            <a-button v-if="record.status === 20 && record.approveStatus != 1" type="text" size="mini"
                                @click="voidContract(record)">作废</a-button>
                            <a-button v-permission="['rent:contract:more:renewal']"
                                v-if="[30, 40].includes(record.status)" type="text" size="mini"
                                @click="renewContract(record)">续签</a-button>
                            <a-button v-if="[30].includes(record.status) && record.approveStatus != 1" type="text"
                                size="mini" @click="changeContract(record)">变更</a-button>
                            <a-dropdown @select="handleRefund($event, record)"
                                v-if="record.status === 30 && !record.terminateId">
                                <a-button v-permission="['rent:contract:quit']" type="text" size="mini" color="#333">
                                    退租
                                </a-button>
                                <template #content>
                                    <a-doption :key="0" :value="0">到期退租</a-doption>
                                    <a-doption :key="1" :value="1">提前退租</a-doption>
                                </template>
                            </a-dropdown>
                            <a-button v-permission="['rent:contract:quit:info']" type="text" size="mini" color="#333"
                                v-if="record.status === 30 && record.terminateId"
                                @click="handleViewTerminationDetail(record)">退租详情</a-button>
                            <a-button v-if="[20, 30].includes(record.status) && record.contractPurpose === 10" type="text"
                                size="mini" @click="expandContract(record)">扩租</a-button>
                            <a-button v-if="[20, 30].includes(record.status) && record.contractPurpose === 10" type="text"
                                size="mini" @click="swapContract(record)">换房</a-button>
                            <!-- <a-button v-if="record.status === 30" type="text" size="mini"
                                @click="quitContract(record)">退租</a-button> -->
                            <!-- <a-button v-if="record.status === 40" type="text" size="mini"
                                @click="quitContractDetail(record)">退租详情</a-button> -->
                            <!-- <a-dropdown v-if="['pending', 'effective', 'invalid', 'void'].includes(record.status)">
                                <a-button type="text">
                                    更多
                                    <icon-down />
                                </a-button>
                                <template #content>
                                    <a-doption @click="viewSignInfo(record)">查看签署信息</a-doption>
                                    <a-doption @click="downloadSignInfo(record)">下载签署信息</a-doption>
                                    <a-doption v-if="[10, 20, 30].includes(record.status)"
                                        @click="uploadAttachment(record)">附件上传</a-doption>
                                    <a-doption v-if="[10, 20, 30, 40].includes(record.status)"
                                        @click="viewAttachment(record)">附件查看/下载</a-doption>
                                    <a-doption v-if="[10, 20, 30].includes(record.status)"
                                        @click="confirmAttachment(record)">附件确认</a-doption>
                                    <a-doption v-if="[10, 20, 30].includes(record.status)"
                                        @click="confirmPaperFile(record)">确收纸质文件</a-doption>
                                    <a-doption v-if="[20, 30].includes(record.status)"
                                        @click="adjustSignMethod(record)">签约方式调整</a-doption>
                                    <a-doption v-if="[20, 30].includes(record.status)"
                                        @click="handleElectronicSign(record)">电子签链接查看/重新发送</a-doption>
                                    <a-doption @click="viewBill(record)">查看账单</a-doption>
                                    <a-doption v-if="[20, 30].includes(record.status)"
                                        @click="adjustPaymentBank(record)">调整付款银行</a-doption>
                                    <a-doption v-if="[20, 30].includes(record.status)"
                                        @click="adjustContact(record)">调整联系人</a-doption>
                                    <a-doption v-if="[20, 30, 40, 50].includes(record.status)"
                                        @click="viewApprovalFlow(record)">查看审批流</a-doption>
                                    <a-doption v-if="[20, 30, 40, 50].includes(record.status)"
                                        @click="viewHistory(record)">查看历史版本</a-doption>
                                </template>
        </a-dropdown> -->
                        </a-space>
                    </template>
                </a-table>
            </a-card>
        </a-card>

        <!-- 合同退租抽屉 -->
        <ContractTerminationMain ref="contractTerminationRef" v-model="contractTerminationVisible"
            @save="handleTerminationSave" @submit="handleTerminationSubmit" @close="handleTerminationClose"
            @refresh="handleSearch" />

        <!-- 创建合同 -->
        <addContractDrawer ref="createContractRef" @submit="handleSearch" />
        <!-- 合同详情 -->
        <contractDetailDrawer ref="contractDetailRef" @submit="handleSearch" />
        <!-- 更新责任人 -->
        <updateOwner ref="updateOwnerRef" @submit="updateOwnerSuccess" />
        <!-- 扩租弹窗 -->
        <expandContractDrawer ref="expandContractRef" @submit="handleSearch" />
        <!-- 换房弹窗 -->
        <swapContractDrawer ref="swapContractRef" @submit="handleSearch" />
        <!-- 收款码弹窗 -->
        <a-modal v-model:visible="collectCodeVisible" title="合同收款码" :footer="false" @cancel="handleCollectCancel"
            :width="500">
            <div class="collect-code-container">
                <div class="collect-code-header">
                    <div class="title">扫码支付账单</div>
                    <div class="contract-info">
                        <div class="info-item">
                            <span class="label">合同号：</span>
                            <span class="value">{{ currentRecord?.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">承租方：</span>
                            <span class="value">{{ currentRecord?.customerName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">租赁资源：</span>
                            <span class="value">{{ extractRoomNumber(currentRecord?.roomName) }}</span>
                        </div>
                    </div>
                </div>

                <div class="qrcode-display">
                    <QRCode :value="currentRecord?.paymentUrl || ''" :size="200" :show-placeholder="true"
                        :show-download="!!currentRecord?.paymentUrl" placeholder-text="暂无收款码"
                        @generated="handleQRCodeGenerated" @error="handleQRCodeError" />
                </div>
            </div>
        </a-modal>

        <a-modal v-model:visible="changeTypeVisible" title="变更类型" :on-before-ok="changeTypeBeforeOk"
            @ok="changeTypeHandleOk">
            <a-checkbox-group direction="vertical" v-model="contractStore.changeType">
                <a-checkbox v-permission="['rent:contract:change:subject']" :value="1">主体变更</a-checkbox>
                <a-checkbox v-permission="['rent:contract:change:terms']" :value="2">条款变更</a-checkbox>
                <a-checkbox v-permission="['rent:contract:change:price']" :value="3">费用条款变更</a-checkbox>
            </a-checkbox-group>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, h } from 'vue'
import { Message, Modal, type TableColumnData } from '@arco-design/web-vue'
import AdvancedSearch from '@/components/advancedSearch/index.vue'
import addContractDrawer from './addContractDrawer.vue'
import contractDetailDrawer from './contractDetailDrawer.vue'
// 引入退租组件 src/views/contractManage/components/contractTermination.vue
import ContractTerminationMain from '@/views/contractManage/contractTerminationMain.vue'
import router from '@/router'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import QRCode from '@/components/QRCode/index.vue'
import { getContractList, deleteContract, invalidContract, getContractSummary } from '@/api/contract'
import { getContractTerminateDetail } from '@/api/contractTerminate'
import dayjs from 'dayjs'
import { getDict, getDictLabel } from '@/dict'
import updateOwner from './updateOwner.vue'
import { IconExclamationCircleFill } from '@arco-design/web-vue/es/icon'
import { useContractStore } from '@/store/modules/contract/index'

// import { getContractTerminateDetail } from '@/api/contractTerminate'
import { title } from 'process'

// 引入导入工具函数
import { exportExcel, importExcelFile } from '@/utils/exportUtil';
// 导入房源模板下载和导入接口
import { downloadContractTemplate, importContractTemplate } from '@/api/contract';

import expandContractDrawer from './expandContractDrawer.vue'
import swapContractDrawer from './swapContractDrawer.vue'


const contractStore = useContractStore()
// 变更合同类型
const changeTypeVisible = ref(false)
const changeTypeRecord = ref<any>({})
const changeTypeBeforeOk = () => {
    if (contractStore.changeType.length === 0) {
        Message.error('请选择变更类型')
        return false
    }
    return true
}
const changeTypeHandleOk = () => {
    createContractRef.value?.open('change', '', changeTypeRecord.value)
}
// 变更合同
const changeContract = (record: any) => {
    changeTypeRecord.value = record
    contractStore.changeType = []
    changeTypeVisible.value = true
}

// 获取字典数据
const contract_purpose = getDict('contract_purpose');
const contract_operate_type = getDict('contract_operate_type');
const contract_status = getDict('contract_status');
const contract_sign_type = getDict('contract_sign_type');
const contract_sign_way = getDict('contract_sign_way');

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 从房间名称中提取房间号（第二个-之后的所有内容），支持多个房间名称，超过3个显示...
const extractRoomNumber = (roomName: string | undefined) => {
    if (!roomName) return ''

    // 检查是否包含多个房间名称（用顿号分隔）
    if (roomName.includes('、')) {
        // 处理多个房间名称
        const roomNames = roomName.split('、')
        const extractedNumbers = roomNames.map(name => {
            const parts = name.trim().split('-')
            // 获取第二个-之后的所有内容
            if (parts.length > 2) {
                return parts.slice(2).join('-')
            }
            return name.trim()
        })

        // 如果超过3个房间，只显示前3个并加上...
        if (extractedNumbers.length > 3) {
            return extractedNumbers.slice(0, 3).join('、') + '...'
        }
        return extractedNumbers.join('、')
    } else {
        // 处理单个房间名称
        const parts = roomName.split('-')
        // 获取第二个-之后的所有内容
        if (parts.length > 2) {
            return parts.slice(2).join('-')
        }
        return roomName
    }
}

const currentProject = ref<any>({})

const handleProjectChange = (value: string, selectedOrg: any) => {
    searchForm.projectId = value
    currentProject.value = selectedOrg

    handleSearch()
}

// 分页数据
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
})
// 分页方法
const onPageChange = (current: number) => {
    pagination.value.current = current
    getTableData()
}
const onPageSizeChange = (pageSize: number) => {
    pagination.value.current = 1
    pagination.value.pageSize = pageSize
    getTableData()
}
// 表格列定义
const columns = computed(() => {
    let operateWidth = 200
    let baseColumns: TableColumnData[] = []
    switch (activeContractStatus.value) {
        // 全部
        case -1:
            operateWidth = 415
            baseColumns = [
                {
                    title: '项目',
                    dataIndex: 'projectName',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同状态',
                    dataIndex: 'statusTwo',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_status_detail', record.statusTwo.toString())
                    },
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('diversification_purpose', record.contractPurpose.toString())
                    }
                },
                {
                    title: '合同号',
                    dataIndex: 'contractNo',
                    width: 260,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '租赁资源',
                    dataIndex: 'roomName',
                    width: 160,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '承租方名称',
                    dataIndex: 'customerName',
                    width: 130,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同类型',
                    dataIndex: 'contractType',
                    width: 100,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_type', record.contractType)
                    }
                },
                {
                    title: '租赁起止日期',
                    dataIndex: 'startDate',
                    width: 220,
                    ellipsis: true,
                    tooltip: true,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return `${record.startDate} 至 ${record.endDate}`
                    }
                },
                {
                    title: '签约类型',
                    dataIndex: 'signType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_type', record.signType)
                    }
                },
                // {
                //     title: '签约金额',
                //     dataIndex: 'totalPrice',
                //     width: 140,
                //     align: 'right',
                //     render: ({ record }: { record: any }) => {
                //         return formatAmount(record.totalPrice)
                //     }
                // },
                {
                    title: '合同面积（㎡）',
                    dataIndex: 'totalArea',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '保证金总额',
                    dataIndex: 'totalBond',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalBond)
                    }
                },
                {
                    title: '租金总额',
                    dataIndex: 'totalPrice',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalPrice)
                    }
                },
                {
                    title: '签约日期',
                    dataIndex: 'signDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '退租日期',
                    dataIndex: 'terminateDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '合同审批状态',
                    dataIndex: 'approveStatus',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_approve_status', record.approveStatus)
                    }
                },
                {
                    title: '签约方式',
                    dataIndex: 'signWay',
                    width: 180,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_way', record.signWay)
                    }
                },
                {
                    title: '是否上传签署文件',
                    dataIndex: 'isUploadSignature',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isUploadSignature ? '是' : '否'
                    }
                },
                {
                    title: '签署文件是否确认',
                    dataIndex: 'isSignatureConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isSignatureConfirm ? '是' : '否'
                    }
                },
                {
                    title: '纸质文件是否确认',
                    dataIndex: 'isPaperConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isPaperConfirm ? '是' : '否'
                    }
                },
                {
                    title: '录入日期',
                    dataIndex: 'createTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '合同签约人',
                    dataIndex: 'signerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '当前责任人',
                    dataIndex: 'ownerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最新操作类型',
                    dataIndex: 'operateType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_operate_type', record.operateType)
                    }
                },
                {
                    title: '最后操作人',
                    dataIndex: 'updateByName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作时间',
                    dataIndex: 'updateTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    fixed: 'right',
                    width: operateWidth,
                    align: 'center'
                }
            ]
            break
        // 草稿
        case 10:
            operateWidth = 200
            baseColumns = [
                {
                    title: '项目',
                    dataIndex: 'projectName',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('diversification_purpose', record.contractPurpose.toString())
                    }
                },
                {
                    title: '合同号',
                    dataIndex: 'contractNo',
                    width: 260,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '租赁资源',
                    dataIndex: 'roomName',
                    width: 160,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '承租方名称',
                    dataIndex: 'customerName',
                    width: 130,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同类型',
                    dataIndex: 'contractType',
                    width: 100,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_type', record.contractType)
                    }
                },
                {
                    title: '租赁起止日期',
                    dataIndex: 'startDate',
                    width: 220,
                    ellipsis: true,
                    tooltip: true,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return `${record.startDate} 至 ${record.endDate}`
                    }
                },
                {
                    title: '签约类型',
                    dataIndex: 'signType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_type', record.signType)
                    }
                },
                // {
                //     title: '签约金额',
                //     dataIndex: 'totalPrice',
                //     width: 140,
                //     align: 'right',
                //     render: ({ record }: { record: any }) => {
                //         return formatAmount(record.totalPrice)
                //     }
                // },
                {
                    title: '合同面积（㎡）',
                    dataIndex: 'totalArea',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '保证金总额',
                    dataIndex: 'totalBond',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalBond)
                    }
                },
                {
                    title: '租金总额',
                    dataIndex: 'totalPrice',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalPrice)
                    }
                },
                {
                    title: '签约日期',
                    dataIndex: 'signDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '签约方式',
                    dataIndex: 'signWay',
                    width: 180,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_way', record.signWay)
                    }
                },
                {
                    title: '合同签约人',
                    dataIndex: 'signerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '当前责任人',
                    dataIndex: 'ownerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '录入日期',
                    dataIndex: 'createTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '最后操作人',
                    dataIndex: 'updateByName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作时间',
                    dataIndex: 'updateTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    fixed: 'right',
                    width: operateWidth,
                    align: 'center'
                }
            ]
            break
        // 待生效
        case 20:
            operateWidth = 300
            baseColumns = [
                {
                    title: '项目',
                    dataIndex: 'projectName',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同状态',
                    dataIndex: 'statusTwo',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_status_detail', record.statusTwo.toString())
                    },
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('diversification_purpose', record.contractPurpose.toString())
                    }
                },
                {
                    title: '合同号',
                    dataIndex: 'contractNo',
                    width: 260,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '租赁资源',
                    dataIndex: 'roomName',
                    width: 160,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '承租方名称',
                    dataIndex: 'customerName',
                    width: 130,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同类型',
                    dataIndex: 'contractType',
                    width: 100,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_type', record.contractType)
                    }
                },
                {
                    title: '租赁起止日期',
                    dataIndex: 'startDate',
                    width: 220,
                    ellipsis: true,
                    tooltip: true,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return `${record.startDate} 至 ${record.endDate}`
                    }
                },
                {
                    title: '签约类型',
                    dataIndex: 'signType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_type', record.signType)
                    }
                },
                // {
                //     title: '签约金额',
                //     dataIndex: 'totalPrice',
                //     width: 140,
                //     align: 'right',
                //     render: ({ record }: { record: any }) => {
                //         return formatAmount(record.totalPrice)
                //     }
                // },
                {
                    title: '合同面积（㎡）',
                    dataIndex: 'totalArea',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '保证金总额',
                    dataIndex: 'totalBond',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalBond)
                    }
                },
                {
                    title: '租金总额',
                    dataIndex: 'totalPrice',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalPrice)
                    }
                },
                {
                    title: '签约日期',
                    dataIndex: 'signDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '合同审批状态',
                    dataIndex: 'approveStatus',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_approve_status', record.approveStatus)
                    }
                },
                {
                    title: '签约方式',
                    dataIndex: 'signWay',
                    width: 180,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_way', record.signWay)
                    }
                },
                {
                    title: '是否上传签署文件',
                    dataIndex: 'isUploadSignature',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isUploadSignature ? '是' : '否'
                    }
                },
                {
                    title: '签署文件是否确认',
                    dataIndex: 'isSignatureConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isSignatureConfirm ? '是' : '否'
                    }
                },
                {
                    title: '纸质文件是否确认',
                    dataIndex: 'isPaperConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isPaperConfirm ? '是' : '否'
                    }
                },
                {
                    title: '录入日期',
                    dataIndex: 'createTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '合同签约人',
                    dataIndex: 'signerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '当前责任人',
                    dataIndex: 'ownerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最新操作类型',
                    dataIndex: 'operateType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_operate_type', record.operateType)
                    }
                },
                {
                    title: '最后操作人',
                    dataIndex: 'updateByName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作时间',
                    dataIndex: 'updateTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    fixed: 'right',
                    width: operateWidth,
                    align: 'center'
                }
            ]
            break
        // 生效
        case 30:
            operateWidth = 415
            baseColumns = [
                {
                    title: '项目',
                    dataIndex: 'projectName',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('diversification_purpose', record.contractPurpose.toString())
                    }
                },
                {
                    title: '合同号',
                    dataIndex: 'contractNo',
                    width: 260,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '租赁资源',
                    dataIndex: 'roomName',
                    width: 160,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '承租方名称',
                    dataIndex: 'customerName',
                    width: 130,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同类型',
                    dataIndex: 'contractType',
                    width: 100,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_type', record.contractType)
                    }
                },
                {
                    title: '租赁起止日期',
                    dataIndex: 'startDate',
                    width: 220,
                    ellipsis: true,
                    tooltip: true,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return `${record.startDate} 至 ${record.endDate}`
                    }
                },
                {
                    title: '签约类型',
                    dataIndex: 'signType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_type', record.signType)
                    }
                },
                // {
                //     title: '签约金额',
                //     dataIndex: 'totalPrice',
                //     width: 140,
                //     align: 'right',
                //     render: ({ record }: { record: any }) => {
                //         return formatAmount(record.totalPrice)
                //     }
                // },
                {
                    title: '合同面积（㎡）',
                    dataIndex: 'totalArea',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '保证金总额',
                    dataIndex: 'totalBond',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalBond)
                    }
                },
                {
                    title: '租金总额',
                    dataIndex: 'totalPrice',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalPrice)
                    }
                },
                {
                    title: '签约日期',
                    dataIndex: 'signDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '合同审批状态',
                    dataIndex: 'approveStatus',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_approve_status', record.approveStatus)
                    }
                },
                {
                    title: '签约方式',
                    dataIndex: 'signWay',
                    width: 180,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_way', record.signWay)
                    }
                },
                {
                    title: '是否上传签署文件',
                    dataIndex: 'isUploadSignature',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isUploadSignature ? '是' : '否'
                    }
                },
                {
                    title: '签署文件是否确认',
                    dataIndex: 'isSignatureConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isSignatureConfirm ? '是' : '否'
                    }
                },
                {
                    title: '纸质文件是否确认',
                    dataIndex: 'isPaperConfirm',
                    width: 150,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isPaperConfirm ? '是' : '否'
                    }
                },
                {
                    title: '录入日期',
                    dataIndex: 'createTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '合同签约人',
                    dataIndex: 'signerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '当前责任人',
                    dataIndex: 'ownerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最新操作类型',
                    dataIndex: 'operateType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_operate_type', record.operateType)
                    }
                },
                {
                    title: '最后操作人',
                    dataIndex: 'updateByName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作时间',
                    dataIndex: 'updateTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    fixed: 'right',
                    width: operateWidth,
                    align: 'center'
                }
            ]
            break
        // 失效
        case 40:
            operateWidth = 150
            baseColumns = [
                {
                    title: '项目',
                    dataIndex: 'projectName',
                    width: 120,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同状态',
                    dataIndex: 'statusTwo',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_status_detail', record.statusTwo.toString())
                    },
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '是否完全结束',
                    dataIndex: 'isFinish',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.isFinish ? '是' : '否'
                    }
                },
                {
                    title: '合同用途',
                    dataIndex: 'contractPurpose',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('diversification_purpose', record.contractPurpose.toString())
                    }
                },
                {
                    title: '合同号',
                    dataIndex: 'contractNo',
                    width: 260,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '租赁资源',
                    dataIndex: 'roomName',
                    width: 160,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '承租方名称',
                    dataIndex: 'customerName',
                    width: 130,
                    align: 'center',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '合同类型',
                    dataIndex: 'contractType',
                    width: 100,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_type', record.contractType)
                    }
                },
                {
                    title: '租赁起止日期',
                    dataIndex: 'startDate',
                    width: 220,
                    ellipsis: true,
                    tooltip: true,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return `${record.startDate} 至 ${record.endDate}`
                    }
                },
                {
                    title: '签约类型',
                    dataIndex: 'signType',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return getDictLabel('contract_sign_type', record.signType)
                    }
                },
                {
                    title: '失效日期',
                    dataIndex: 'invalidDate',
                    width: 120,
                    align: 'center'
                },
                // {
                //     title: '签约金额',
                //     dataIndex: 'totalPrice',
                //     width: 140,
                //     align: 'right',
                //     render: ({ record }: { record: any }) => {
                //         return formatAmount(record.totalPrice)
                //     }
                // },
                {
                    title: '合同面积（㎡）',
                    dataIndex: 'totalArea',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '保证金总额',
                    dataIndex: 'totalBond',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalBond)
                    }
                },
                {
                    title: '租金总额',
                    dataIndex: 'totalPrice',
                    width: 130,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return formatAmount(record.totalPrice)
                    }
                },
                {
                    title: '签约日期',
                    dataIndex: 'signDate',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '合同签约人',
                    dataIndex: 'signerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '当前责任人',
                    dataIndex: 'ownerName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作人',
                    dataIndex: 'updateByName',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '最后操作时间',
                    dataIndex: 'updateTime',
                    width: 120,
                    align: 'center',
                    render: ({ record }: { record: any }) => {
                        return record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD') : ''
                    }
                },
                {
                    title: '操作',
                    slotName: 'operations',
                    fixed: 'right',
                    width: operateWidth,
                    align: 'center'
                }
            ]
            break
    }
    // 只在失效状态标签页下添加"是否完全结束"列
    if (activeContractStatus.value === 40) {
        baseColumns.splice(baseColumns.length - 1, 0, {
            title: '是否完全结束',
            dataIndex: 'isCompletelyFinished',
            width: 120,
            align: 'center'
        })
    }
    return baseColumns
})
// 表格数据
const tableData = ref<TableColumnData[]>([])

// 表格多选
const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false
}
const selectionList = ref<string[]>([])

// 添加选择变化处理函数
const handleSelectionChange = (rowKeys: string[]) => {
    console.log('selection-change:', rowKeys) // 调试日志
    selectionList.value = rowKeys
}

// 获取表格数据
const getTableData = async () => {
    // 查询入参
    const searchParams = JSON.parse(JSON.stringify(searchForm))
    if (searchParams.signDate && searchParams.signDate.length) {
        searchParams.signDateStart = searchParams.signDate[0]
        searchParams.signDateEnd = searchParams.signDate[1]
    }
    delete searchParams.signDate
    const params = {
        pageNum: pagination.value.current,
        pageSize: pagination.value.pageSize,
        ...searchParams
    }
    const result = await getContractList(params)
    tableData.value = result.rows || []
    pagination.value.total = result.total || 0

    // 获取二级状态统计数据
    await getSummaryData()

    // 数据刷新后清空选中状态
    selectionList.value = []
    tableRef.value?.selectAll(false)
}
// 获取二级状态统计数据
const summaryData = ref({
    contractPeriodNotStarted: 0,
    bondNotFullyReceived: 0,
    periodNotStartedAndBondNotReceived: 0,
    normalTermination: 0,
    earlyTermination: 0,
    roomChange: 0,
    renewal: 0,
    normalTerminationPendingCheckout: 0,
    earlyTerminationPendingCheckout: 0,
    pendingTermination: 0,
    invalid: 0,
    invalidRenewal: 0
})

// 获取二级状态统计数据
const getSummaryData = async () => {
    try {
        // 查询入参
        const searchParams = JSON.parse(JSON.stringify(searchForm))
        if (searchParams.signDate && searchParams.signDate.length) {
            searchParams.signDateStart = searchParams.signDate[0]
            searchParams.signDateEnd = searchParams.signDate[1]
        }
        delete searchParams.signDate
        const params = {
            pageNum: pagination.value.current,
            pageSize: pagination.value.pageSize,
            ...searchParams
        }

        const { data } = await getContractSummary(params)
        if (data) {
            summaryData.value = {
                contractPeriodNotStarted: data.contractPeriodNotStarted || 0,
                bondNotFullyReceived: data.bondNotFullyReceived || 0,
                periodNotStartedAndBondNotReceived: data.periodNotStartedAndBondNotReceived || 0,
                normalTermination: data.normalTermination || 0,
                earlyTermination: data.earlyTermination || 0,
                roomChange: data.roomChange || 0,
                renewal: data.renewal || 0,
                normalTerminationPendingCheckout: data.normalTerminationPendingCheckout || 0,
                earlyTerminationPendingCheckout: data.earlyTerminationPendingCheckout || 0,
                pendingTermination: data.pendingTermination || 0,
                invalid: data.invalid || 0,
                invalidRenewal: data.invalidRenewal || 0
            }
            console.log(summaryData.value);
        }
    } catch (error) {
        console.error('获取二级状态统计数据失败:', error)
    }
}

// 高级检索
const isCollapsed = ref(true)
// 搜索表单数据
// 定义类型
interface SearchForm {
    projectId: string;
    contractType: number | undefined;
    roomName: string;
    customerName: string;
    contractPurposes: number[];
    ownerName: string;
    operateTypes: number[];
    statuses: number[];
    statusTwos: number[];
    contractNo: string;
    signType: number | undefined;
    signWays: number[];
    signDate: string[];
    isUploadSignature: boolean | undefined;
    isPaperConfirm: boolean | undefined;
    isFinish: boolean | undefined;
}
const searchForm = reactive<SearchForm>({
    projectId: '',
    contractType: undefined,
    roomName: '',
    customerName: '',
    contractPurposes: [],
    ownerName: '',
    operateTypes: [],
    statuses: [10],
    statusTwos: [],
    contractNo: '',
    signType: undefined,
    signWays: [],
    signDate: [],
    isUploadSignature: undefined,
    isPaperConfirm: undefined,
    isFinish: undefined
})
// 搜索方法
const handleSearch = () => {
    pagination.value.current = 1
    getTableData()
}
// 重置表单
const resetForm = () => {
    Object.keys(searchForm).filter(key => key !== 'projectId').forEach(key => {
        (searchForm as any)[key] = Array.isArray((searchForm as any)[key]) ? [] : ''
    })
    if (activeContractStatus.value === -1) {
        searchForm.statuses = []
    } else if (activeContractStatus.value === 40) {
        searchForm.statuses = [40, 50]
    } else {
        searchForm.statuses = [activeContractStatus.value]
    }
    pagination.value.current = 1
    getTableData()
}

// 添加监听合同状态选择框的变化
watch(
    () => searchForm.statuses,
    (newStatuses) => {
        // 当合同状态选择框被清空时，切换到"全部"Tab
        if (!newStatuses || newStatuses.length === 0) {
            activeContractStatus.value = -1
        } else if (newStatuses.length === 1) {
            // 当只选择了一个状态时，切换到对应的Tab
            const status = newStatuses[0]
            if ([10, 20, 30].includes(status)) {
                activeContractStatus.value = status
            } else if ([40, 50].includes(status)) {
                activeContractStatus.value = 40
            }
        } else if (newStatuses.length === 2 && newStatuses.includes(40) && newStatuses.includes(50)) {
            // 当选择了失效状态的两个值[40, 50]时，保持在失效Tab
            activeContractStatus.value = 40
        } else {
            // 当选择了其他多个状态时，切换到"全部"Tab
            activeContractStatus.value = -1
        }
    },
    { deep: true }
)

// 合同类型Tab
const contractTypeOptions = [
    { label: '全部', value: -1 },
    { label: '非宿舍', value: 0 },
    { label: '宿舍类', value: 1 },
    { label: '多经', value: 2 },
    // { label: '日租房', value: 3 }
]
const activeContractType = ref<number>(-1)

const changeContractType = () => {
    pagination.value.current = 1
    searchForm.contractType = activeContractType.value === -1 ? undefined : activeContractType.value
    getTableData()
}

// 合同状态Tab
const contractStatusOptions = [
    { label: '全部', value: -1 },
    { label: '草稿', value: 10 },
    { label: '待生效', value: 20 },
    { label: '生效', value: 30 },
    { label: '失效', value: 40 }
]
const activeContractStatus = ref<number>(10)
const changeContractStatus = () => {
    searchForm.statuses = activeContractStatus.value === -1 ? [] : activeContractStatus.value === 40 ? [40, 50] : [activeContractStatus.value]
    pagination.value.current = 1
    getTableData()
}

// 合同详情抽屉
const contractDetailRef = ref()

// 更新责任人
const tableRef = ref()
const updateOwnerRef = ref()
const updateResponsible = () => {
    console.log('当前选中的合同:', selectionList.value) // 添加调试日志
    if (selectionList.value.length === 0) {
        Message.warning('请选择合同')
        return
    }
    updateOwnerRef.value.open(selectionList.value)
}
const updateOwnerSuccess = () => {
    selectionList.value = []
    tableRef.value?.selectAll(false)
    handleSearch()
}

// 导出数据
const exportData = () => {
    try {
        // 获取所有标题
        const headers = [
            "项目", "合同状态", "合同用途", "合同号", "租赁资源", "承租方名称", "合同类型",
            "租赁起止日期", "签约类型", "签约金额", "签约日期", "退租日期",
            "合同审批状态", "最新操作类型", "签约方式", "是否上传签署文件",
            "签署文件是否确认", "纸质文件是否确认", "合同签约人", "当前责任人",
            "录入日期", "最后操作人", "最后操作时间"
        ];

        // 如果在失效标签页下，添加"是否完全结束"字段
        if (activeContractStatus.value === 40) {
            headers.push("是否完全结束");
        }

        // 创建HTML表格
        let htmlContent = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
        htmlContent += '<head><meta charset="UTF-8"></head><body>';
        htmlContent += '<table border="1">';

        // 添加表头（带背景色）
        htmlContent += '<tr style="background-color: #4C8BF5; color: white; font-weight: bold;">';
        headers.forEach(header => {
            htmlContent += `<th>${header}</th>`;
        });
        htmlContent += '</tr>';

        // 添加数据行
        tableData.value.forEach((row: any) => {
            // 处理租赁起止日期
            const leaseDate = `${row.startDate || ''} 至 ${row.endDate || ''}`;

            // 处理各种映射字段
            const contractTypeText = getDictLabel('contract_type', row.contractType) || '';
            const signTypeText = getDictLabel('contract_sign_type', row.signType) || '';
            const approveStatusText = getDictLabel('contract_approve_status', row.approveStatus) || '';
            const operateTypeText = getDictLabel('contract_operate_type', row.operateType) || '';
            const signWayText = getDictLabel('contract_sign_way', row.signWay) || '';
            const contractPurposeText = getDictLabel('diversification_purpose', row.contractPurpose?.toString()) || '';

            // 处理布尔值字段
            const isUploadSignatureText = row.isUploadSignature ? '是' : '否';
            const isSignatureConfirmText = row.isSignatureConfirm ? '是' : '否';
            const isPaperConfirmText = row.isPaperConfirm ? '是' : '否';

            // 处理日期字段
            const createTimeText = row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD') : '';
            const updateTimeText = row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD') : '';

            // 处理金额字段
            const totalPriceText = formatAmount(row.totalPrice);
            // 合同状态
            const contractStatusText = getDictLabel('contract_status_detail', row.statusTwo.toString())
            // 构建数据行
            htmlContent += '<tr>';

            // 添加所有单元格数据（使用正确的字段名）
            htmlContent += `<td>${row.projectName || ''}</td>`;
            htmlContent += `<td>${contractStatusText}</td>`;
            htmlContent += `<td>${contractPurposeText}</td>`;
            htmlContent += `<td>${row.contractNo || ''}</td>`;
            htmlContent += `<td>${row.roomName || ''}</td>`;
            htmlContent += `<td>${row.customerName || ''}</td>`;
            htmlContent += `<td>${contractTypeText}</td>`;
            htmlContent += `<td>${leaseDate}</td>`;
            htmlContent += `<td>${signTypeText}</td>`;
            htmlContent += `<td>${totalPriceText}</td>`;
            htmlContent += `<td>${row.signDate || ''}</td>`;
            htmlContent += `<td>${row.terminateDate || ''}</td>`;
            htmlContent += `<td>${approveStatusText}</td>`;
            htmlContent += `<td>${operateTypeText}</td>`;
            htmlContent += `<td>${signWayText}</td>`;
            htmlContent += `<td>${isUploadSignatureText}</td>`;
            htmlContent += `<td>${isSignatureConfirmText}</td>`;
            htmlContent += `<td>${isPaperConfirmText}</td>`;
            htmlContent += `<td>${row.signerName || ''}</td>`;
            htmlContent += `<td>${row.ownerName || ''}</td>`;
            htmlContent += `<td>${createTimeText}</td>`;
            htmlContent += `<td>${row.updateByName || ''}</td>`;
            htmlContent += `<td>${updateTimeText}</td>`;

            // 如果在失效标签页下，添加"是否完全结束"字段
            if (activeContractStatus.value === 40) {
                htmlContent += `<td>${row.isCompletelyFinished || ''}</td>`;
            }

            htmlContent += '</tr>';
        });

        htmlContent += '</table></body></html>';

        // 创建Blob对象
        const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
        const url = window.URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = `合同列表_${new Date().getTime()}.xls`;
        document.body.appendChild(link);
        link.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
    } catch (error) {
        console.error('导出失败:', error);
    }
}

// 新增合同
const createContractRef = ref()
const handleAddContract = (contractType: number) => {
    if (!searchForm.projectId) {
        Message.warning('请选择项目')
        return
    }
    createContractRef.value.open('create', contractType, '', currentProject.value)
}

// 查看合同
const viewContract = (record: any) => {
    contractDetailRef.value.open(record)
}

// 编辑合同
const editContract = (record: any) => {
    createContractRef.value.open('edit', '', record)
}

// 删除合同
const handleDeleteContract = (record: any) => {
    // 只有草稿状态的合同才能删除
    if (record.status !== 10) {
        Message.warning('只有草稿状态的合同才能删除')
        return
    }

    Modal.info({
        title: '提示',
        content: () => {
            return h('div', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }
            }, [h(IconExclamationCircleFill, {
                style: {
                    color: 'rgb(var(--warning-6))',
                    fontSize: '22px'
                }
            }), '是否确认执行当前删除操作？'])
        },
        okText: '确认',
        cancelText: '取消',
        hideCancel: false,
        alignCenter: false,
        simple: false,
        maskClosable: false,
        modalClass: 'confirm-popup',
        width: 400,
        onOk: async () => {
            const loading = Message.loading('删除中...')
            try {
                await deleteContract(record.id)
                loading.close()
                Message.success('删除成功')
                // 重新加载数据
                getTableData()
            } catch (error: any) {
                loading.close()
                console.error('删除合同失败:', error)
            }
        }
    })
}

// 作废合同
const voidContract = (record: any) => {
    // 只有待生效状态的合同才能作废
    if (record.status !== 20) {
        Message.warning('只有待生效状态的合同才能作废')
        return
    }

    Modal.info({
        title: '提示',
        content: () => {
            return h('div', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }
            }, [h(IconExclamationCircleFill, {
                style: {
                    color: 'rgb(var(--warning-6))',
                    fontSize: '22px'
                }
            }), '是否确认执行当前作废操作？'])
        },
        okText: '确认作废',
        cancelText: '取消',
        hideCancel: false,
        alignCenter: false,
        simple: false,
        maskClosable: false,
        modalClass: 'confirm-popup',
        width: 400,
        onOk: async () => {
            const loading = Message.loading('作废中...')
            try {
                await invalidContract(record.id)
                loading.close()
                Message.success('作废成功')
                // 重新加载数据
                getTableData()
            } catch (error: any) {
                loading.close()
                console.error('作废合同失败:', error)
            }
        }
    })
}

// 续签
const renewContract = (record: any) => {
    createContractRef.value.open('renew', '', record)
}

// 退租
const contractTerminationVisible = ref(false)
const contractTerminationRef = ref()

const handleViewTerminationDetail = async (record: any) => {
    console.log('退租详情:', record)
    const res = await getContractTerminateDetail({ id: record.terminateId })
    console.log('退租详情:', res)
    if (res && res.code === 200) {
        const responseData = res.data // 完整的响应数据
        // isExit
        // boolean 
        // 是否同时办理出场:0-否,1-是
        // 0-否,1-是
        // 0-否,1-是
        contractTerminationRef.value.open({
            contractId: record.id,
            terminateId: record.terminateId,
            mode: responseData.isExit ? 'apply-and-exit' : 'apply-only',
            refundType: responseData.terminateType,
            // 是否可以取消退租：只有待审核状态（approveStatus === 0）时才能取消
            canCancel: responseData.approveStatus === 0
        })
    }
    // contractTerminationRef.value.open({
    //     contractId: record.id,
    //     terminateId: record.terminateId,
    // })
}

// 生效中的合同支持退租操作；点击“退租”按钮后，选择到期退租或提前退租；选择后，出现退租弹窗，选择先申请退租，后续结账/退租并出场结算；点击取消关闭弹窗
// 生效的合同支持退租操作；点击“退租”按钮后，选择到期退租或提前退租；选择后，出现退租弹窗，选择先申请退租，后续结账/退租并出场结算；点击取消关闭弹窗
// 若选择到期退租，则默认走退租并出场结算流程，不需要展示选择流程的弹窗；
const handleRefund = (value: any, record: any) => {
    console.log('退租:', value, record)
    // 只有生效状态的合同才能退租
    if (record.status !== 30) {
        Message.warning('只有生效状态的合同才能退租')
        return
    }
    if (value === 0) {
        // 到期退租
        // 若选择到期退租，则默认走退租并出场结算流程，不需要展示选择流程的弹窗；
        contractTerminationRef.value.open({
            contractId: record.id,
            terminateId: record.terminateId,
            mode: 'apply-and-exit',
            refundType: 0,
            canCancel: false
        })
    } else if (value === 1) {
        // 提前退租
        // 弹出确认弹窗
        const modal = Modal.open({
            title: '退租',
            content: () => {
                return h('div', {}, [
                    h('div', {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            marginBottom: '16px'
                        }
                    }, [
                        h('span', {
                            style: {
                                width: '20px',
                                height: '20px',
                                borderRadius: '50%',
                                backgroundColor: '#ff7d00',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '12px',
                                fontWeight: 'bold'
                            }
                        }, '!'),
                        h('span', {
                            style: {
                                width: '0',
                                flex: 1
                            }
                        }, [
                            h('span', { style: { fontWeight: 'bold' } }, '先申请退租，后续结账：'),
                            '先发起退租申请，确认退租日期，暂时不做出场和结算，仅提醒该房源预计释放日期'
                        ])
                    ]),
                    h('div', {
                        style: {
                            marginLeft: '28px',
                            // color: '#666'
                        }
                    }, [
                        h('span', { style: { fontWeight: 'bold' } }, '退租并出场结算：'),
                        '实际退租时发起，完成退租流程后，房源释放'
                    ])
                ])
            },
            width: 520,
            footer: () => {
                return h('div', {
                    style: {
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '8px',
                        // padding: '16px 24px',
                        // borderTop: '1px solid #e5e6eb'
                    }
                }, [
                    h('button', {
                        class: 'arco-btn arco-btn-secondary',
                        onClick: () => {
                            // 关闭弹窗
                            modal.close()
                        }
                    }, '取消'),
                    h('button', {
                        class: 'arco-btn arco-btn-primary',
                        onClick: () => {
                            // 先申请退租，后续结账
                            modal.close()
                            if (contractTerminationRef.value) {
                                contractTerminationRef.value.open({
                                    contractId: record.id,
                                    terminateId: record.terminateId,
                                    mode: 'apply-only',
                                    refundType: 1,
                                    canCancel: false
                                })
                            }
                        }
                    }, '先申请退租，后续结账'),
                    h('button', {
                        class: 'arco-btn arco-btn-primary',
                        onClick: () => {
                            // 退租并出场结算
                            modal.close()
                            if (contractTerminationRef.value) {
                                contractTerminationRef.value.open({
                                    contractId: record.id,
                                    terminateId: record.terminateId,
                                    mode: 'apply-and-exit',
                                    refundType: 1,
                                    canCancel: false
                                })
                            }
                        }
                    }, '退租并出场结算')
                ])
            }
        })
    }


}

// 处理退租暂存
const handleTerminationSave = (data: any) => {
    console.log('退租暂存:', data)
    getTableData()
    Message.success('退租申请暂存成功')
}

// 处理退租提交
const handleTerminationSubmit = (data: any) => {
    console.log('退租提交:', data)
    Message.success('退租申请提交成功')
    // 刷新列表
    getTableData()
}

// 处理退租抽屉关闭
const handleTerminationClose = () => {
    contractTerminationVisible.value = false
}

// 收款码弹窗控制
const collectCodeVisible = ref(false)
const currentRecord = ref<any>({})

// 打开收款码弹窗
const handleCollect = async (record: any) => {
    try {
        // 生成支付链接
        const baseUrl = import.meta.env.VITE_APP_BASE_URL
        const contractId = record.id
        const amount = record.totalPrice
        const customerName = encodeURIComponent(record.customerName || '')

        const paymentLink = `${baseUrl}/bill-payment?id=${contractId}&amount=${amount}&customer=${customerName}&type=bond`

        currentRecord.value = {
            ...record,
            paymentUrl: paymentLink
        }
        collectCodeVisible.value = true
    } catch (error) {
        console.error('生成收款码失败:', error)
        Message.error('生成收款码失败')
    }
}

// 关闭收款码弹窗
const handleCollectCancel = () => {
    collectCodeVisible.value = false
    currentRecord.value = {}
}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    Message.error('二维码生成失败: ' + error.message)
    console.error('二维码生成失败:', error)
}

// 扩租
const expandContractRef = ref()
const expandContract = (record: any) => {
    console.log('扩租:', record)
    expandContractRef.value.open(record)
}
// 处理下载模板
const handleDownloadTemplate = () => {

    try {
        // 使用exportExcel方法下载模板
        exportExcel(downloadContractTemplate, null, '合同导入模板')
            .then(() => {
                Message.success('模板下载成功');
            })
    } catch (error) {
        console.error('下载模板失败:', error);
    }
};
// 导入合同
const importData = () => {

    // 创建文件选择器
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.style.display = 'none';

    // 监听文件选择事件
    input.onchange = (event) => {
        const target = event.target as HTMLInputElement;
        if (target.files && target.files.length > 0) {
            const file = target.files[0];

            // 检查文件类型
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                Message.error('请上传Excel文件(.xlsx或.xls)');
                return;
            }

            // 显示加载状态
            const loadingInstance = Message.loading({
                content: '正在导入合同数据...',
                duration: 0
            });

            // 使用importExcelFile方法导入文件
            importExcelFile(file, importContractTemplate)
                .then((res: any) => {
                    if (res && res.code === 200) {
                        Message.success('合同导入成功');
                        // 刷新列表
                        handleSearch();
                    }
                })
                .catch((error: any) => {
                    console.error('合同导入失败:', error);
                })
                .finally(() => {
                    // 关闭加载状态
                    loadingInstance.close();
                    // 移除input元素
                    document.body.removeChild(input);
                });
        }
    };

    // 添加到DOM并触发点击
    document.body.appendChild(input);
    input.click();
}
// 换房
const swapContractRef = ref()
const swapContract = (record: any) => {
    console.log('换房:', record)
    swapContractRef.value.open(record)
}

</script>

<style lang="less" scoped>
.container {
    padding: 0 16px;

    .table-header {
        margin-bottom: 16px;

        .operation-group {
            display: flex;
            gap: 8px;
        }
    }
}

:deep(.arco-tag),
:deep(.arco-select-view-input) {
    transition: none !important;
}

.collect-code-container {
    padding: 24px;
    text-align: center;

    .collect-code-header {
        margin-bottom: 24px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1d2129;
        }

        .contract-info {
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                padding: 0 16px;

                .label {
                    color: #86909c;
                    font-size: 14px;
                }

                .value {
                    color: #1d2129;
                    font-size: 14px;
                    font-weight: 500;

                    &.amount {
                        color: #f53f3f;
                        font-weight: 600;
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }
}
</style>

<style lang="less">
.confirm-popup {
    .arco-modal-footer {
        border-top: none;
    }
}
</style>