import http from './index'

const systemUrl = '/business-rent-admin'

// 智能水电相关类型定义

// 基础分页参数
export interface BasePageParams {
    pageNum: number
    pageSize: number
}

// 通用响应结果
export interface AjaxResult<T = any> {
    error: boolean
    success: boolean
    warn: boolean
    empty: boolean
    data?: T
    msg?: string
    code?: number
}

// 房源查询参数（用于水电房源列表）
export interface WaterElectricityQueryDTO extends BasePageParams {
    params?: Record<string, any>
    projectId?: string
    parcelId?: string
    buildingId?: string
    searchParam?: string // 楼栋/房间名称
    roomName?: string // 房间名称（模糊搜索）
    bindFlag?: number // 0：未绑定，1：已绑定
}

// 房源信息（用于水电）
export interface RoomSimpleForWaterVo {
    roomId: string
    roomName: string
    fullName: string
    projectId: string
    projectName: string
    parcelId: string
    parcelName: string
    buildingId: string
    buildingName: string
    floorId: string
    floorName: string
    opsSysRoomId: string
    opsSysRoomName: string
}

// 房间水电用量查询参数
export interface RoomWaterElectricityLogQueryDTO extends BasePageParams {
    params?: Record<string, any>
    id?: string
    roomId?: string
    coolUseNum?: number
    coolTotal?: number
    hotUseNum?: number
    hotTotal?: number
    eleUseNum?: number
    eleTotal?: number
    totalReserve?: number
    total?: number
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    isDel?: boolean
    dateType?: number // 1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年
    startDate?: string
    endDate?: string
}

// 房间水电用量日志信息
export interface RoomWaterElectricityLogVo {
    id: string
    roomId: string
    coolUseNum: number
    coolTotal: number
    hotUseNum: number
    hotTotal: number
    eleUseNum: number
    eleTotal: number
    totalReserve: number
    total: number
    createByName: string
    createTime: string
    updateByName: string
    isDel: boolean
}

// 房间信息（完整）
export interface RoomVo {
    id: string
    roomName: string
    type: number
    propertyType: string
    propertyTypeName: string
    projectId: string
    projectName: string
    parcelId: string
    parcelName: string
    buildingId: string
    buildingName: string
    floorId: string
    floorName: string
    roomCode: string
    roomId: string
    status: number
    isLock: boolean
    isDirty: boolean
    isMaintain: boolean
    remark: string
    mergeSplitId: string
    planEffectDate: string
    actualEffectDate: string
    projectPriceId: string
    tablePrice: number
    bottomPrice: number
    areaType: number
    buildArea: number
    innerArea: number
    rentAreaType: number
    rentArea: number
    storey: number
    value: number
    orientation: string
    houseTypeId: string
    houseTypeName: string
    smartWaterMeter: string
    smartLock: string
    assetOperationMode: string
    assetOperationType: string
    propertyStatus: string
    paymentStatus: number
    specialTag: string
    latestRentPrice: number
    latestRentStartDate: string
    latestRentEndDate: string
    firstRentPrice: number
    firstRentStartDate: string
    firstRentEndDate: string
    operationSubject: number
    rentalStartDate: string
    externalRentStartDate: string
    isSelfUse: boolean
    selfUseSubject: number
    selfUsePurpose: string
    djBindRoomId: string
    djBindName: string
    createByName: string
    updateByName: string
    isDel: boolean
    orientationName: string
    priceUnit: number
    baseRent: number
    additionalFee: number
}

// 房间水电详情信息
export interface RoomWaterElectricityDetailVo {
    roomInfo: RoomVo
    waterElectricityLogList: RoomWaterElectricityLogVo[]
}

/**
 * 查询房源信息列表
 * @param params 查询参数
 * @returns Promise<RoomSimpleForWaterVo[]>
 */
export function getWaterElectricityRoomList(params: WaterElectricityQueryDTO) {
    return http.post<RoomSimpleForWaterVo[]>(`${systemUrl}/water/electricity/room/list`, params)
}

/**
 * 房源信息列表导入
 * @param file 文件对象
 * @returns Promise<AjaxResult>
 */
export function importWaterElectricityRoomList(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return http.post<AjaxResult>(`${systemUrl}/water/electricity/room/import`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 智能水电列表接口
 * @param params 查询参数
 * @returns Promise<RoomVo[]>
 */
export function getWaterElectricityList(params: WaterElectricityQueryDTO) {
    return http.get<RoomVo[]>(`${systemUrl}/water/electricity/list`, { params })
}

/**
 * 智能水电详情
 * @param roomId 房间ID
 * @returns Promise<RoomWaterElectricityDetailVo>
 */
export function getWaterElectricityRoomDetail(roomId: string) {
    return http.get<RoomWaterElectricityDetailVo>(`${systemUrl}/water/electricity/detail`, { params: { roomId } })
}

/**
 * 智能水电记录
 * @param params 查询参数
 * @returns Promise<RoomWaterElectricityLogVo[]>
 */
export function getWaterElectricityLogList(params: RoomWaterElectricityLogQueryDTO) {
    return http.post<RoomWaterElectricityLogVo[]>(`${systemUrl}/water/electricity/log/detail`, params)
}

/**
 * 保存关联关系接口
 * @param data 绑定数据
 * @returns Promise<boolean>
 */
export function saveWaterElectricityBindRelation(data: RoomSimpleForWaterVo[]) {
    return http.post<boolean>(`${systemUrl}/water/electricity/room/bind`, data)
}

/**
 * 解绑房间与运营系统的关联关系
 * @param roomIds 房间ID列表
 * @returns Promise<boolean>
 */
export function deleteWaterElectricityBindRelation(roomIds: string[]) {
    return http.post<boolean>(`${systemUrl}/water/electricity/room/bind/delete`, roomIds)
}

/**
 * 导出房间水电用量列表
 * @param params 查询参数
 */
export function exportWaterElectricityList(params: WaterElectricityQueryDTO) {
    return http.post(`${systemUrl}/water/electricity/room/export`, params, { responseType: 'blob' })
}
