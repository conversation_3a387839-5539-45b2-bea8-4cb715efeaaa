import http from './index'

const systemUrl = '/business-rent-admin'

// 智能门锁相关类型定义

// 基础分页参数
export interface BasePageParams {
    pageNum: number
    pageSize: number
}

// 通用响应结果
export interface AjaxResult<T = any> {
    error: boolean
    success: boolean
    warn: boolean
    empty: boolean
    data?: T
    msg?: string
    code?: number
}

// 分页响应结果
export interface TableDataInfo<T = any> {
    total: number
    rows: T[]
    code: number
    msg: string
}

// 房源查询参数
export interface RoomSimpleQueryDTO extends BasePageParams {
    params?: Record<string, any>
    projectId?: string
    parcelId?: string
    buildingId?: string
    roomName?: string
    bindFlag?: number // 0-未绑定，1-已绑定
}

// 房源信息
export interface RoomSimpleVo {
    roomId: string
    roomName: string
    parcelName: string
    buildingName: string
    fullName: string
    ttlockDeviceId: string
    ttlockId: number
    ttlockName: string
}

// 智能门锁设备查询参数
export interface TTLockDeviceQueryParams {
    id?: string
    projectId?: string
    roomOrBuildingName?: string
    username?: string
    lockId?: number
    lockName?: string
    lockAlias?: string
    lockMac?: string
    electricQuantity?: number
    roomId?: string
    bindTime?: string
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    isDel?: boolean
    pageNum: number
    pageSize: number
}

// 智能门锁设备信息
export interface TTLockDeviceVo {
    id: string
    projectId: string
    projectName: string
    parcelId: string
    parcelName: string
    buildingId: string
    buildingName: string
    roomName: string
    propertyType: string
    username: string
    lockId: number
    lockName: string
    lockAlias: string
    lockMac: string
    electricQuantity: number
    roomId: string
    bindTime: string
    createByName: string
    updateByName: string
    isDel: boolean
    operateLogList?: TTLockOperateLogVo[]
}

// 操作日志查询参数
export interface TTLockOperateLogQueryDTO extends BasePageParams {
    params?: Record<string, any>
    id?: string
    deviceId?: string
    roomId?: string
    password?: string
    type?: number
    dateType?: number // 1-近7天，2-近半月，3-近一月，4-近三月，5-近半年，6-近一年
    startDate?: string
    endDate?: string
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    isDel?: boolean
}

// 操作日志信息
export interface TTLockOperateLogVo {
    id: string
    deviceId: string
    roomId: string
    password: string
    type: number
    startDate: string
    endDate: string
    createByName: string
    createTime: string
    updateByName: string
    isDel: boolean
}

// 通通锁账号查询参数
export interface TTLockAccountQueryDTO extends BasePageParams {
    params?: Record<string, any>
    id?: string
    projectId?: string
    brandType?: number // 1-通通锁
    username?: string
    lockUsername?: string // 新增字段
    password?: string
    createBy?: string
    createByName?: string
    createTime?: string
    updateBy?: string
    updateByName?: string
    updateTime?: string
    isDel?: boolean
    accountIdList?: string[]
}

// 通通锁账号新增参数
export interface TTLockAccountAddDTO {
    id?: string
    projectId: string
    brandType: number // 1-通通锁
    username: string
    password: string
    isDel?: boolean
}

// 设备信息查询参数
export interface TTLockDeviceInfoQueryDTO extends BasePageParams {
    params?: Record<string, any>
    accountList?: string[]
    deviceName?: string
}

// 同步设备数据参数 - 使用TTLockAccountQueryDTO
export type TTLockSyncDeviceDTO = TTLockAccountQueryDTO

/**
 * 查询房源信息列表
 * @param params 查询参数
 * @returns Promise<TableDataInfo<RoomSimpleVo>>
 */
export function getRoomList(params: RoomSimpleQueryDTO) {
    return http.post<TableDataInfo<RoomSimpleVo>>(`${systemUrl}/ttlock/room/list`, params)
}

/**
 * 导出房源信息列表
 * @param params 查询参数
 */
export function exportRoomList(params: RoomSimpleQueryDTO) {
    return http.post(`${systemUrl}/ttlock/room/export`, params, { responseType: 'blob' })
}

/**
 * 智能门锁密码记录
 * @param params 查询参数
 * @returns Promise<AjaxResult<TTLockOperateLogVo[]>>
 */
export function getLockOperateLog(params: TTLockOperateLogQueryDTO) {
    return http.post<AjaxResult<TTLockOperateLogVo[]>>(`${systemUrl}/ttlock/log/detail`, params)
}

/**
 * 获取临时密码
 * @param deviceId 设备ID
 * @returns Promise<AjaxResult>
 */
export function generateTempPassword(deviceId: string) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/generate/password`, {}, { params: { deviceId } })
}

/**
 * 同步设备数据
 * @param params 同步参数
 * @returns Promise<AjaxResult>
 */
export function syncDeviceData(params: TTLockSyncDeviceDTO) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/sync`, params)
}

/**
 * 保存关联关系
 * @param data 绑定数据
 * @returns Promise<AjaxResult>
 */
export function bindRoomDevice(data: RoomSimpleVo[]) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/room/bind`, data)
}

/**
 * 解绑房间与设备的绑定关系
 * @param roomIds 房间ID列表
 * @returns Promise<AjaxResult>
 */
export function unbindRoomDevice(roomIds: string[]) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/room/bind/delete`, roomIds)
}

/**
 * 自动匹配房间与设备的绑定关系
 * @param projectId 项目ID
 * @returns Promise<AjaxResult>
 */
export function autoBindRoomDevice(projectId: string) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/device/room/auto/bind`, {}, { params: { projectId } })
}

/**
 * 获取设备信息
 * @param params 查询参数
 * @returns Promise<TableDataInfo<TTLockDeviceVo>>
 */
export function getDeviceInfo(params: TTLockDeviceInfoQueryDTO) {
    return http.post<TableDataInfo<TTLockDeviceVo>>(`${systemUrl}/ttlock/device/info`, params)
}

/**
 * 导出通通锁设备列表
 * @param params 查询参数
 */
export function exportTTLockDeviceList(params: TTLockDeviceInfoQueryDTO) {
    return http.post(`${systemUrl}/ttlock/device/export`, params, { responseType: 'blob' })
}

/**
 * 新增通通锁账号
 * @param data 账号数据
 * @returns Promise<AjaxResult>
 */
export function addTTLockAccount(data: TTLockAccountAddDTO) {
    return http.post<AjaxResult>(`${systemUrl}/ttlock/account/save`, data)
}

/**
 * 查询通通锁设备列表
 * @param params 查询参数
 * @returns Promise<TableDataInfo<TTLockDeviceVo>>
 */
export function getTTLockDeviceList(params: TTLockDeviceQueryParams) {
    return http.get<TableDataInfo<TTLockDeviceVo>>(`${systemUrl}/ttlock/device/list`, params)
}

/**
 * 智能门锁详情
 * @param deviceId 设备ID
 * @returns Promise<AjaxResult>
 */
export function getTTLockDetail(deviceId: string) {
    return http.get<AjaxResult>(`${systemUrl}/ttlock/device/detail`, { deviceId })
}

/**
 * 查询通通锁账号列表
 * @param params 查询参数
 * @returns Promise<AjaxResult>
 */
export function getTTLockAccountList(params: TTLockAccountQueryDTO) {
    return http.get<AjaxResult>(`${systemUrl}/ttlock/account/list`, params)
}

/**
 * 删除通通锁账号
 * @param id 账号ID
 * @returns Promise<AjaxResult>
 */
export function deleteTTLockAccount(id: string) {
    return http.delete<AjaxResult>(`${systemUrl}/ttlock/account/delete`, { id: id })
}

/**
 * 房源设备绑定导入
 * @param file 文件对象
 * @returns Promise<AjaxResult>
 */
export function importRoomDeviceBind(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return http.post<AjaxResult>(`${systemUrl}/ttlock/room/import`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}
