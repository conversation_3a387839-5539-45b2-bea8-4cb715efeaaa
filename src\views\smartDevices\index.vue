<template>
  <div class="container">
    <a-card class="general-card">
      <!-- Tab切换 -->
      <a-tabs v-model:activeTab="activeTab" @change="handleTabChange">
        <a-tab-pane key="water-electric" title="智能水电" />
        <a-tab-pane key="door-lock" title="智能门锁" />
      </a-tabs>

      <!-- 统一筛选栏 -->
      <a-row>
        <a-col :flex="1">
          <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
            <a-row :gutter="16">
              <!-- 项目 -->
              <a-col :span="8">
                <a-form-item field="projectId" label="项目">
                  <ProjectTreeSelect
                    v-model="filterForm.projectId"
                    :min-level="4"
                    @change="handleProjectChange"
                  />
                </a-form-item>
              </a-col>
              <!-- 楼栋/房源 -->
              <a-col :span="8">
                <a-form-item field="buildingOrRoom" label="楼栋/房源">
                  <a-input v-model="filterForm.buildingOrRoom" placeholder="请输入名称" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon><icon-search /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0;" />

      <!-- 操作按钮 -->
      <div class="action-bar">
        <!-- 智能水电操作按钮 -->
        <a-space v-if="activeTab === 'water-electric'">
          <a-button type="primary" @click="handleWaterElectricBind">
            批量绑定设备
          </a-button>
          <a-button @click="handleWaterElectricUnbind">
            批量解绑
          </a-button>
        </a-space>
        <!-- 智能门锁操作按钮 -->
        <a-space v-if="activeTab === 'door-lock'">
          <a-button type="primary" @click="handleDoorLockBind">
            批量绑定设备
          </a-button>
          <a-button @click="handleDoorLockUnbind">
            批量解绑
          </a-button>
        </a-space>
      </div>

      <!-- 根据activeTab显示不同的表格 -->
      <water-electric-tab v-if="activeTab === 'water-electric'"
        ref="waterElectricTabRef"
        :filter-form="filterForm"
        v-model:selected-keys="waterElectricSelectedKeys"
        @update:loading="loading = $event"
        @edit-device="handleWaterElectricEdit"
        @view-detail="handleWaterElectricDetail"
      />
      <door-lock-tab v-if="activeTab === 'door-lock'"
        ref="doorLockTabRef"
        :filter-form="filterForm"
        v-model:selected-keys="doorLockSelectedKeys"
        @update:loading="loading = $event"
        @edit-device="handleDoorLockEdit"
        @view-detail="handleDoorLockDetail"
      />
    </a-card>

    <!-- 智能水电批量绑定抽屉 -->
    <WaterElectricBatchBindDrawer
      v-if="waterElectricBatchBindVisible"
      ref="waterElectricBatchBindDrawerRef"
      @success="handleWaterElectricBatchBindSuccess"
      @cancel="handleWaterElectricBatchBindCancel"
    />

    <!-- 智能门锁批量绑定抽屉 -->
    <BatchBindDrawer
      v-if="doorLockBatchBindVisible"
      ref="doorLockBatchBindDrawerRef"
      device-type="door-lock"
      @success="handleDoorLockBatchBindSuccess"
      @cancel="handleDoorLockBatchBindCancel"
    />

    <!-- 智能水电设备详情抽屉 -->
    <WaterElectricDetailDrawer
      v-if="waterElectricDetailVisible"
      ref="waterElectricDetailDrawerRef"
      @cancel="handleWaterElectricDetailCancel"
      @refresh="handleWaterElectricDetailRefresh"
    />

    <!-- 智能门锁设备详情抽屉 -->
    <DeviceDetailDrawer
      v-if="doorLockDetailVisible"
      ref="doorLockDetailDrawerRef"
      device-type="door-lock"
      @cancel="handleDoorLockDetailCancel"
      @refresh="handleDoorLockDetailRefresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconSearch,
  IconRefresh
} from '@arco-design/web-vue/es/icon'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import WaterElectricTab from './components/WaterElectricTab.vue'
import DoorLockTab from './components/DoorLockTab.vue'
import BatchBindDrawer from './components/BatchBindDrawer.vue'
import DeviceDetailDrawer from './components/DeviceDetailDrawer.vue'
import WaterElectricBatchBindDrawer from './components/WaterElectricBatchBindDrawer.vue'
import WaterElectricDetailDrawer from './components/WaterElectricDetailDrawer.vue'
import { unbindRoomDevice } from '@/api/smartLock'
import { deleteWaterElectricityBindRelation } from '@/api/smartWaterElectricity'

// 当前激活的tab
const activeTab = ref('water-electric')

// 筛选表单
const filterForm = reactive({
  projectId: '',
  buildingOrRoom: ''
})

// 智能水电选中的行
const waterElectricSelectedKeys = ref<string[]>([])

// 智能门锁选中的行
const doorLockSelectedKeys = ref<string[]>([])

// 加载状态
const loading = ref(false)

// 子组件引用
const waterElectricTabRef = ref()
const doorLockTabRef = ref()
const waterElectricBatchBindDrawerRef = ref()
const doorLockBatchBindDrawerRef = ref()
const waterElectricDetailDrawerRef = ref()
const doorLockDetailDrawerRef = ref()

// 初始化状态
const isInit = ref(false)

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})

// 智能水电批量绑定抽屉状态
const waterElectricBatchBindVisible = ref(false)

// 智能门锁批量绑定抽屉状态
const doorLockBatchBindVisible = ref(false)

// 智能水电详情抽屉状态
const waterElectricDetailVisible = ref(false)

// 智能门锁详情抽屉状态
const doorLockDetailVisible = ref(false)

// 调用当前激活tab的列表接口
const loadData = async () => {
  await nextTick()
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.fetchTableData()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.fetchTableData()
  }
}

// Tab切换处理
const handleTabChange = (key: string) => {
  activeTab.value = key
  // 清空两个tab的选中项
  waterElectricSelectedKeys.value = []
  doorLockSelectedKeys.value = []
  // Tab切换时，如果有项目ID则调用查询
  if (filterForm.projectId) {
    loadData()
  }
}

// 项目变更处理 - 参考 reduction/index.vue
const handleProjectChange = (value: string | number, selectedOrg: any) => {
  console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

  // 存储项目信息
  currentProject.value = {
    projectId: value,
    projectName: selectedOrg?.name || ''
  }

  filterForm.projectId = value as string

  // 只有在有项目ID时才自动触发搜索
  if (value && !isInit.value) {
    isInit.value = true
    // 重置分页到第一页
    if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
      waterElectricTabRef.value.resetPagination()
    } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
      doorLockTabRef.value.resetPagination()
    }
    loadData()
  }
}

// 查询处理
const handleSearch = () => {
  console.log('查询条件:', filterForm)
  // 重置分页到第一页
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.resetPagination()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.resetPagination()
  }
  loadData()
}

// 重置处理
const handleReset = () => {
  filterForm.buildingOrRoom = ''
  // 清空两个tab的选中项
  waterElectricSelectedKeys.value = []
  doorLockSelectedKeys.value = []
  // 重置后不自动调用查询，等待用户选择项目
  handleSearch()
}

// ========== 智能水电相关方法 ==========

// 智能水电批量绑定设备
const handleWaterElectricBind = async () => {
  if (!filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }

  waterElectricBatchBindVisible.value = true
  await nextTick()
  // 调用子组件的 show 方法传递项目信息
  waterElectricBatchBindDrawerRef.value?.show({
    projectId: filterForm.projectId,
    projectName: currentProject.value.projectName
  })
}

// 智能水电批量绑定成功回调
const handleWaterElectricBatchBindSuccess = () => {
  waterElectricBatchBindVisible.value = false
  // 刷新当前tab的数据
  loadData()
  Message.success('智能水电批量绑定完成')
}

// 智能水电批量绑定取消回调
const handleWaterElectricBatchBindCancel = () => {
  waterElectricBatchBindVisible.value = false
}

// 智能水电批量解绑
const handleWaterElectricUnbind = () => {
  if (waterElectricSelectedKeys.value.length === 0) {
    Message.warning('请选择要解绑的智能水电设备')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑选中的 ${waterElectricSelectedKeys.value.length} 个智能水电设备吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await deleteWaterElectricityBindRelation(waterElectricSelectedKeys.value)
        Message.success('智能水电解绑成功')
        // 清空选中项
        waterElectricSelectedKeys.value = []
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('智能水电解绑失败:', error)
      }
    }
  })
}

// 智能水电编辑设备（解绑）
const handleWaterElectricEdit = (record: any) => {
  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑智能水电设备"${record.roomName}"吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await deleteWaterElectricityBindRelation([record.roomId])
        Message.success('智能水电解绑成功')
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('智能水电解绑失败:', error)
      }
    }
  })
}

// 智能水电查看详情
const handleWaterElectricDetail = async (record: any) => {
  waterElectricDetailVisible.value = true
  await nextTick()
  waterElectricDetailDrawerRef.value?.show(record)
}

// 智能水电详情抽屉取消回调
const handleWaterElectricDetailCancel = () => {
  waterElectricDetailVisible.value = false
}

// 智能水电详情抽屉刷新回调
const handleWaterElectricDetailRefresh = () => {
  loadData()
}

// ========== 智能门锁相关方法 ==========

// 智能门锁批量绑定设备
const handleDoorLockBind = async () => {
  if (!filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }

  doorLockBatchBindVisible.value = true
  await nextTick()
  // 调用子组件的 show 方法传递项目信息
  doorLockBatchBindDrawerRef.value?.show({
    projectId: filterForm.projectId,
    projectName: currentProject.value.projectName
  })
}

// 智能门锁批量绑定成功回调
const handleDoorLockBatchBindSuccess = () => {
  doorLockBatchBindVisible.value = false
  // 刷新当前tab的数据
  loadData()
  Message.success('智能门锁批量绑定完成')
}

// 智能门锁批量绑定取消回调
const handleDoorLockBatchBindCancel = () => {
  doorLockBatchBindVisible.value = false
}

// 智能门锁批量解绑
const handleDoorLockUnbind = () => {
  if (doorLockSelectedKeys.value.length === 0) {
    Message.warning('请选择要解绑的智能门锁设备')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑选中的 ${doorLockSelectedKeys.value.length} 个智能门锁设备吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice(doorLockSelectedKeys.value)
        Message.success('智能门锁解绑成功')
        // 清空选中项
        doorLockSelectedKeys.value = []
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('智能门锁解绑失败:', error)
      }
    }
  })
}

// 智能门锁编辑设备（解绑）
const handleDoorLockEdit = (record: any) => {
  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑智能门锁设备"${record.roomName}"吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice([record.id])
        Message.success('智能门锁解绑成功')
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('智能门锁解绑失败:', error)
      }
    }
  })
}

// 智能门锁查看详情
const handleDoorLockDetail = async (record: any) => {
  doorLockDetailVisible.value = true
  await nextTick()
  doorLockDetailDrawerRef.value?.show(record)
}

// 智能门锁详情抽屉取消回调
const handleDoorLockDetailCancel = () => {
  doorLockDetailVisible.value = false
}

// 智能门锁详情抽屉刷新回调
const handleDoorLockDetailRefresh = () => {
  loadData()
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="less" scoped>
.container {
  padding: 0 20px;
}

.action-bar {
  margin-bottom: 16px;
}
</style>