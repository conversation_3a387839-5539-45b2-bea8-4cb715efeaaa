<template>
  <div class="container">
    <a-card class="general-card">
      <!-- Tab切换 -->
      <a-tabs v-model:activeTab="activeTab" @change="handleTabChange">
        <a-tab-pane key="water-electric" title="智能水电" />
        <a-tab-pane key="door-lock" title="智能门锁" />
      </a-tabs>

      <!-- 统一筛选栏 -->
      <a-row>
        <a-col :flex="1">
          <a-form label-align="right" :model="filterForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
            <a-row :gutter="16">
              <!-- 项目 -->
              <a-col :span="8">
                <a-form-item field="projectId" label="项目">
                  <ProjectTreeSelect
                    v-model="filterForm.projectId"
                    :min-level="4"
                    @change="handleProjectChange"
                  />
                </a-form-item>
              </a-col>
              <!-- 楼栋/房源 -->
              <a-col :span="8">
                <a-form-item field="buildingOrRoom" label="楼栋/房源">
                  <a-input v-model="filterForm.buildingOrRoom" placeholder="请输入名称" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon><icon-search /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin: 0 0 16px 0;" />

      <!-- 操作按钮 -->
      <div class="action-bar">
        <a-space>
          <a-button type="primary" @click="handleBindDevice">
            批量绑定设备
          </a-button>
          <a-button @click="handleUnbindDevice">
            批量解绑
          </a-button>
        </a-space>
      </div>

      <!-- 根据activeTab显示不同的表格 -->
      <water-electric-tab v-if="activeTab === 'water-electric'"
        ref="waterElectricTabRef"
        :filter-form="filterForm"
        v-model:selected-keys="selectedKeys"
        @update:loading="loading = $event"
        @edit-device="handleEditDevice"
        @view-detail="handleViewDetail"
      />
      <door-lock-tab v-if="activeTab === 'door-lock'"
        ref="doorLockTabRef"
        :filter-form="filterForm"
        v-model:selected-keys="selectedKeys"
        @update:loading="loading = $event"
        @edit-device="handleEditDevice"
        @view-detail="handleViewDetail"
      />
    </a-card>

    <!-- 批量绑定抽屉 -->
    <BatchBindDrawer
      v-if="batchBindVisible"
      ref="batchBindDrawerRef"
      @success="handleBatchBindSuccess"
      @cancel="handleBatchBindCancel"
    />

    <!-- 设备详情抽屉 -->
    <DeviceDetailDrawer
      v-if="detailVisible"
      ref="deviceDetailDrawerRef"
      @cancel="handleDetailCancel"
      @refresh="handleDetailRefresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconSearch,
  IconRefresh
} from '@arco-design/web-vue/es/icon'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import WaterElectricTab from './components/WaterElectricTab.vue'
import DoorLockTab from './components/DoorLockTab.vue'
import BatchBindDrawer from './components/BatchBindDrawer.vue'
import DeviceDetailDrawer from './components/DeviceDetailDrawer.vue'
import { unbindRoomDevice } from '@/api/smartLock'

// 当前激活的tab
const activeTab = ref('water-electric')

// 筛选表单
const filterForm = reactive({
  projectId: '',
  buildingOrRoom: ''
})

// 选中的行
const selectedKeys = ref<string[]>([])

// 加载状态
const loading = ref(false)

// 子组件引用
const waterElectricTabRef = ref()
const doorLockTabRef = ref()
const batchBindDrawerRef = ref()
const deviceDetailDrawerRef = ref()

// 初始化状态
const isInit = ref(false)

// 存储当前选中的项目信息
const currentProject = ref<any>({
    projectId: '',
    projectName: ''
})

// 批量绑定抽屉状态
const batchBindVisible = ref(false)

// 详情抽屉状态
const detailVisible = ref(false)

// 调用当前激活tab的列表接口
const loadData = async () => {
  await nextTick()
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.fetchTableData()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.fetchTableData()
  }
}

// Tab切换处理
const handleTabChange = (key: string) => {
  activeTab.value = key
  selectedKeys.value = []
  // Tab切换时，如果有项目ID则调用查询
  if (filterForm.projectId) {
    loadData()
  }
}

// 项目变更处理 - 参考 reduction/index.vue
const handleProjectChange = (value: string | number, selectedOrg: any) => {
  console.log('项目变化，检查是否有项目ID再调用列表', value, selectedOrg)

  // 存储项目信息
  currentProject.value = {
    projectId: value,
    projectName: selectedOrg?.name || ''
  }

  filterForm.projectId = value as string

  // 只有在有项目ID时才自动触发搜索
  if (value && !isInit.value) {
    isInit.value = true
    // 重置分页到第一页
    if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
      waterElectricTabRef.value.resetPagination()
    } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
      doorLockTabRef.value.resetPagination()
    }
    loadData()
  }
}

// 查询处理
const handleSearch = () => {
  console.log('查询条件:', filterForm)
  // 重置分页到第一页
  if (activeTab.value === 'water-electric' && waterElectricTabRef.value) {
    waterElectricTabRef.value.resetPagination()
  } else if (activeTab.value === 'door-lock' && doorLockTabRef.value) {
    doorLockTabRef.value.resetPagination()
  }
  loadData()
}

// 重置处理
const handleReset = () => {
  filterForm.buildingOrRoom = ''
  selectedKeys.value = []
  // 重置后不自动调用查询，等待用户选择项目
  handleSearch()
}

// 批量绑定设备
const handleBindDevice = async () => {
  if (!filterForm.projectId) {
    Message.warning('请先选择项目')
    return
  }

  if (activeTab.value !== 'door-lock') {
    Message.warning('批量绑定功能仅支持智能门锁')
    return
  }

  batchBindVisible.value = true
  await nextTick()
  // 调用子组件的 show 方法传递项目信息
  batchBindDrawerRef.value?.show({
    projectId: filterForm.projectId,
    projectName: currentProject.value.projectName
  })
}

// 批量绑定成功回调
const handleBatchBindSuccess = () => {
  batchBindVisible.value = false
  // 刷新当前tab的数据
  loadData()
  Message.success('批量绑定完成')
}

// 批量绑定取消回调
const handleBatchBindCancel = () => {
  batchBindVisible.value = false
}

// 批量解绑
const handleUnbindDevice = () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请选择要解绑的设备')
    return
  }

  if (activeTab.value !== 'door-lock') {
    Message.warning('批量解绑功能仅支持智能门锁')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑选中的 ${selectedKeys.value.length} 个设备吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice(selectedKeys.value)
        Message.success('解绑成功')
        // 清空选中项
        selectedKeys.value = []
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('解绑失败:', error)
      }
    }
  })
}

// 编辑设备（解绑）
const handleEditDevice = (record: any) => {
  if (activeTab.value !== 'door-lock') {
    Message.warning('解绑功能仅支持智能门锁')
    return
  }

  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑设备"${record.roomName}"吗？解绑后将无法恢复。`,
    onOk: async () => {
      try {
        await unbindRoomDevice([record.id])
        Message.success('解绑成功')
        // 刷新数据
        loadData()
      } catch (error) {
        console.error('解绑失败:', error)
      }
    }
  })
}

// 查看详情
const handleViewDetail = async (record: any) => {
  if (activeTab.value !== 'door-lock') {
    Message.warning('详情功能仅支持智能门锁')
    return
  }

  detailVisible.value = true
  await nextTick()
  deviceDetailDrawerRef.value?.show(record)
}

// 详情抽屉取消回调
const handleDetailCancel = () => {
  detailVisible.value = false
}

// 详情抽屉刷新回调
const handleDetailRefresh = () => {
  loadData()
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="less" scoped>
.container {
  padding: 0 20px;
}

.action-bar {
  margin-bottom: 16px;
}
</style>