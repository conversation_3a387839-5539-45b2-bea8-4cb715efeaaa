import http from './index';

/**
 * 客户类型
 */
export enum CustomerType {
    /** 个人 */
    PERSONAL = 1,
    /** 企业 */
    ENTERPRISE = 2,
}

/**
 * 合同客户对象
 */
export interface ContractCustomerDTO {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 客户id */
    customerId?: string;
    /** 客户类型:1-个人1,2-企业 */
    customerType?: number;
    /** 客户名/公司名 */
    customerName?: string;
    /** 地址 */
    address?: string;
    /** 手机号(个人/法人) */
    phone?: string;
    /** 证件类型(个人/法人) */
    idType?: string;
    /** 证件号码(个人/法人) */
    idNumber?: string;
    /** 是否是员工:0-否,1-是 */
    isEmployee?: boolean;
    /** 统一社会信用代码 */
    creditCode?: string;
    /** 联系人 */
    contactName?: string;
    /** 联系人手机号 */
    contactPhone?: string;
    /** 联系人身份证号 */
    contactIdNumber?: string;
    /** 法人名称 */
    legalName?: string;
    /** 付款银行名称 */
    paymentBank?: string;
    /** 付款银行账号 */
    paymentAccount?: string;
    /** 担保人姓名 */
    guarantorName?: string;
    /** 担保人手机号 */
    guarantorPhone?: string;
    /** 担保人证件类型 */
    guarantorIdType?: string;
    /** 担保人证件号码 */
    guarantorIdNumber?: string;
    /** 担保人通讯地址 */
    guarantorAddress?: string;
    /** 担保人身份证正面地址 */
    guarantorIdFront?: string;
    /** 担保人身份证反面地址 */
    guarantorIdBack?: string;
    /** 开票名称 */
    invoiceTitle?: string;
    /** 开票税号 */
    invoiceTaxNumber?: string;
    /** 开票单位地址 */
    invoiceAddress?: string;
    /** 开票电话号码 */
    invoicePhone?: string;
    /** 开票开户银行 */
    invoiceBankName?: string;
    /** 开票银行账户 */
    invoiceAccountNumber?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 合同定单对象
 */
export interface ContractBookingDTO {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 定单id */
    bookingId?: string;
    /** 预定房源 */
    bookedRoom?: string;
    /** 预定人姓名 */
    bookerName?: string;
    /** 定单已收金额 */
    bookingReceivedAmount?: number;
    /** 定单收款(生效)日期 */
    bookingPaymentDate?: string;
    /** 转保证金金额 */
    transferBondAmount?: number;
    /** 转租金金额 */
    transferRentAmount?: number;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 合同费用对象
 */
export interface ContractFeeDTO {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 费用类型,1-免租期 */
    feeType?: number;
    /** 免租类型,0-装修免租,1-经营免租,2-合同免租 */
    freeType?: number;
    /** 免租月数 */
    freeRentMonth?: number;
    /** 免租天数 */
    freeRentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 备注 */
    remark?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 合同账单对象
 */
export interface ContractCostDTO {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 账单类型,1-保证金,2-租金,3-其他费用 */
    costType?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 账单期数 */
    period: number;
    /** 商户id */
    customerId?: string;
    /** 商户名 */
    customerName?: string;
    /** 商铺id */
    roomId?: string;
    /** 商铺名 */
    roomName?: string;
    /** 商铺面积 */
    area?: number;
    /** 收款用途id */
    subjectId?: string;
    /** 收款用途 */
    subjectName?: string;
    /** 应收日期 */
    receivableDate?: string;
    /** 单价 */
    unitPrice?: number;
    /** 单价单位 */
    priceUnit: number;
    /** 账单总额（元） */
    totalAmount?: number;
    /** 优惠金额（元） */
    discountAmount?: number;
    /** 账单实际应收金额（元） */
    actualReceivable?: number;
    /** 账单已收金额（元） */
    receivedAmount?: number;
    /** 是否营收提成,0-否,1-是 */
    isRevenue?: boolean;
    /** 提成类型: 1-固定提成, 2-阶梯提成 */
    percentageType?: number;
    /** 固定提成比例 */
    fixedPercentage?: number;
    /** 阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}] */
    stepPercentage?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
}

/**
 * 合同房源对象
 */
export interface ContractRoomDTO {
    /** 主键ID */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 房源id */
    roomId: string;
    /** 房间名称 */
    roomName: string;
    /** 面积（㎡） */
    area: number;
    /** 标准租金（单价） */
    standardUnitPrice?: number;
    /** 底价 */
    bottomPrice?: number;
    /** 单价单位, 使用统一字典, 待定 */
    priceUnit: number;
    /** 折扣 */
    discount: number;
    /** 签约单价 */
    signedUnitPrice: number;
    /** 签约月总价（元/月） */
    signedMonthlyPrice: number;
    /** 实际开始日期 */
    startDate?: string;
    /** 实际结束日期 */
    endDate?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 物业类型 */
    propertyType: string;
    /** 保证金类型 */
    bondPriceType?: number;
    depositType?: number;
    /** 保证金金额 */
    bondPrice?: number;
    depositAmount?: number;
    buildingName?: string;
    floorName?: string;
    parcelName?: string;
    stageName?: string;
    type?: number;
    isChecked?: boolean;
}

/**
 * 合同添加DTO
 */
export interface ContractAddDTO {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    projectName?: string;
    /** 合同号 */
    contractNo?: string;
    /** 统一id */
    unionId?: string;
    /** 版本号v00x */
    version?: string;
    /** 是否当前生效版本 0-否,1-是 */
    isCurrent?: boolean;
    /** 一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废 */
    status: number;
    /** 二级状态 */
    statusTwo?: number;
    /** 审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝 */
    approveStatus?: number;
    /** 最新操作类型,0-新签,1-变更条款,2-退租 */
    operateType?: number;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType: number;
    /** 我方签约主体信息 */
    ourSigningId?: string;
    ourSigningParty?: string;
    /** 签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章） */
    signWay?: number;
    /** 签约类型,0-新签,1-续签 */
    signType?: number;
    /** 续签源合同ID */
    originId?: string;
    /** 变更源合同ID */
    changeFromId?: string;
    /** 用地性质 */
    landUsage?: string;
    /** 合同签约人id */
    signerId?: string;
    /** 签约人姓名 */
    signerName?: string;
    /** 责任人id */
    ownerId?: string;
    /** 责任人姓名 */
    ownerName?: string;
    /** 合同模式,0-标准合同,1-非标合同 */
    contractMode?: number;
    /** 纸质合同号 */
    paperContractNo?: string;
    /** 签订日期 */
    signDate?: string;
    /** 交房日期 */
    handoverDate?: string;
    /** 合同用途,字典: */
    contractPurpose?: number;
    /** 成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商 */
    dealChannel?: number;
    /** 协助人id */
    assistantId?: string;
    /** 协助人姓名 */
    assistantName?: string;
    /** 租赁期限-年 */
    rentYear?: number;
    /** 租赁期限-月 */
    rentMonth?: number;
    /** 租赁期限-日 */
    rentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 实际生效日期 */
    effectDate?: string;
    /** 保证金应收日期(天数/具体日期) */
    bondReceivableDate?: string;
    /** 保证金应收日期类型,0-合同签订后, 1-指定日期 */
    bondReceivableType?: number;
    /** 保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金 */
    bondPriceType?: number;
    /** 保证金金额 */
    bondPrice?: number;
    /** 收费方式,0-固定租金,1-递增租金,2-营收抽成 */
    chargeWay?: number;
    /** 租金应收日期(天数/具体日期) */
    rentReceivableDate?: string;
    /** 租金应收日期类型,1-租期开始前,2-租期开始后 */
    rentReceivableType?: number;
    /** 租金账单周期: 1-租赁月, 2-自然月 */
    rentTicketPeriod?: number;
    /** 租金支付周期 */
    rentPayPeriod?: number;
    /** 递增间隔(年) */
    increaseGap?: number;
    /** 递增率 */
    increaseRate?: number;
    /** 租金递增规则(非标) */
    increaseRule?: string;
    /** 预估营收额 */
    estimateRevenue?: number;
    /** 提成类型: 1-固定提成, 2-阶梯提成 */
    percentageType?: number;
    /** 固定提成比例 */
    fixedPercentage?: number;
    /** 阶梯提成比例json信息, eg: [{"amount": 0, "percentage": 3}] */
    stepPercentage?: string;
    /** 抽点类型: 1-按月营业额, 2-按年营业额 */
    revenueType?: number;
    /** 是否包含物业费 */
    isIncludePm?: boolean;
    /** 月物业费单价 */
    pmUnitPrice?: number;
    /** 月物业费总价 */
    pmMonthlyPrice?: number;
    /** 合同总价 */
    totalPrice?: number;
    /** 业态id */
    bizTypeId?: string;
    /** 业态 */
    bizTypeName?: string;
    /** 承租方品牌 */
    lesseeBrand?: string;
    /** 经营品类 */
    businessCategory?: string;
    /** 开业日期 */
    openDate?: string;
    /** 生产火灾危险性类别,0-丙类,1-丁类,2-戊类 */
    fireRiskCategory?: number;
    /** 喷淋系统,0-安装,1-未安装 */
    sprinklerSystem?: number;
    /** 厂房从事 */
    factoryEngaged?: string;
    /** 交接日期 */
    deliverDate?: string;
    /** 车位类型,0-人防,1-非人防 */
    parkingSpaceType?: number;
    /** 是否包含车位管理费 */
    hasParkingFee?: boolean;
    /** 车位管理费金额 */
    parkingFeeAmount?: number;
    /** 补充条款 */
    otherInfo?: string;
    /** 合同附件[{`"fileName": "", "fileUrl":"", "fileType":""}] */
    contractAttachments?: string;
    /** 附件-平面图 */
    attachmentsPlan?: string;
    /** 是否上传签署文件 0-否,1-是 */
    isUploadSignature?: boolean;
    /** 是否确认签署 0-否,1-是 */
    isSignatureConfirm?: boolean;
    /** 是否确认纸质文件 0-否,1-是 */
    isPaperConfirm?: boolean;
    /** 合同是否完全结束 0-否,1-是 */
    isFinish?: boolean;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 0-暂存,1-提交 */
    isSubmit?: boolean;
    /** 合同客户对象 */
    customer: ContractCustomerDTO;
    /** 合同定单对象 */
    bookings?: ContractBookingDTO[];
    /** 合同费用对象 */
    fees: ContractFeeDTO[];
    /** 合同账单对象 */
    costs?: ContractCostDTO[];
    /** 合同房源对象 */
    rooms: ContractRoomDTO[];
    createByName?: string;
    createTime?: string;
    bookingRelType: number;
    venueDeliveryDate?: string;
    venueLocation?: string;
    venuePurpose?: string;
    dailyActivityStartTime?: string;
    dailyActivityEndTime?: string;
    activityDate?: string[];
    changeType?: string;
    changeDate?: string;
    changeExplanation?: string;
    changeAttachments?: string;
    /** 物业费支付周期 */
    pmPayPeriod?: number;
}

/**
 * 客户下拉列表查询参数
 */
export interface CustomerOptionsDTO {
    /** 客户类型，个人：1，企业：2 */
    customerType?: number;
    /** 客户姓名/公司名 */
    customerName?: string;
    /** 统一社会信用代码 */
    creditCode?: string;
    /** 手机号(个人/法人) */
    phone?: string;
    /** 项目id */
    projectId?: string;
    /** 客户id */
    customerId?: string;
}

/**
 * 客户信息
 */
export interface CustomerVo {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 客户类型，个人：1，企业：2 */
    customerType?: number;
    /** 客户姓名，支持身份证识别填充 */
    customerName?: string;
    /** 统一社会信用代码，需校验唯一性 */
    creditCode?: string;
    /** 法人姓名 */
    legalName?: string;
    /** 联系电话，需校验唯一性 */
    contactPhone?: string;
    /** 证件类型，身份证/护照等 */
    idType?: string;
    /** 证件号码，支持身份证识别填充 */
    idNumber?: string;
    /** 证件有效期开始，支持身份证识别填充 */
    idValidityStart?: string;
    /** 证件有效期结束，支持身份证识别填充 */
    idValidityEnd?: string;
    /** 身份证正面地址 */
    idFront?: string;
    /** 身份证反面地址 */
    idBack?: string;
    /** 联系地址，支持身份证识别填充 */
    contactAddress?: string;
    /** 维护人id */
    maintainerId?: string;
    /** 维护人姓名 */
    maintainerName?: string;
    /** 附件 */
    attachmentFiles?: string;
    /** 备注 */
    remark?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0 否 1是 */
    isDel?: number;
}

/**
 * 合同详情VO
 */
export interface ContractVo {
    /** 主键ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 合同号 */
    contractNo?: string;
    /** 统一id */
    unionId?: string;
    /** 版本号v00x */
    version?: string;
    /** 是否当前生效版本 0-否,1-是 */
    isCurrent?: boolean;
    /** 一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废 */
    status?: number;
    /** 二级状态 */
    statusTwo?: number;
    /** 审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝 */
    approveStatus?: number;
    /** 最新操作类型,0-新签,1-变更条款,2-退租 */
    operateType?: number;
    /** 合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房 */
    contractType?: number;
    /** 我方签约主体信息 */
    ourSigningParty?: string;
    /** 签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章） */
    signWay?: number;
    /** 签约类型,0-新签,1-续签 */
    signType?: number;
    /** 续签源合同ID */
    originId?: string;
    /** 变更源合同ID */
    changeFromId?: string;
    /** 用地性质 */
    landUsage?: string;
    /** 合同签约人id */
    signerId?: string;
    /** 签约人姓名 */
    signerName?: string;
    /** 责任人id */
    ownerId?: string;
    /** 责任人姓名 */
    ownerName?: string;
    /** 合同模式,0-标准合同,1-非标合同 */
    contractMode?: number;
    /** 纸质合同号 */
    paperContractNo?: string;
    /** 签订日期 */
    signDate?: string;
    /** 交房日期 */
    handoverDate?: string;
    /** 合同用途,字典: */
    contractPurpose?: number;
    /** 成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商 */
    dealChannel?: number;
    /** 协助人id */
    assistantId?: string;
    /** 协助人姓名 */
    assistantName?: string;
    /** 租赁期限-年 */
    rentYear?: number;
    /** 租赁期限-月 */
    rentMonth?: number;
    /** 租赁期限-日 */
    rentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 实际生效日期 */
    effectDate?: string;
    /** 保证金应收日期(天数/具体日期) */
    bondReceivableDate?: string;
    /** 保证金应收日期类型,0-合同签订后, 1-指定日期 */
    bondReceivableType?: number;
    /** 保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金 */
    bondPriceType?: number;
    /** 保证金金额 */
    bondPrice?: number;
    /** 收费方式,0-固定租金,1-递增租金,2-营收抽成 */
    chargeWay?: number;
    /** 租金应收日期(天数/具体日期) */
    rentReceivableDate?: string | number;
    /** 租金应收日期类型,1-租期开始前,2-租期开始后 */
    rentReceivableType?: number;
    /** 租金账单周期: 1-租赁月, 2-自然月 */
    rentTicketPeriod?: number;
    /** 租金支付周期 */
    rentPayPeriod?: number;
    /** 递增间隔(年) */
    increaseGap?: number;
    /** 递增率 */
    increaseRate?: number;
    /** 租金递增规则(非标) */
    increaseRule?: string;
    /** 预估营收额 */
    estimateRevenue?: number;
    /** 提成类型: 1-固定提成, 2-阶梯提成 */
    percentageType?: number;
    /** 固定提成比例 */
    fixedPercentage?: number;
    /** 阶梯提成比例json信息 */
    stepPercentage?: string;
    /** 抽点类型: 1-按月营业额, 2-按年营业额 */
    revenueType?: number;
    /** 是否包含物业费 */
    isIncludePm?: boolean;
    /** 月物业费单价 */
    pmUnitPrice?: number;
    /** 月物业费总价 */
    pmMonthlyPrice?: number;
    /** 合同总价 */
    totalPrice?: number;
    /** 业态id */
    bizTypeId?: string;
    /** 业态 */
    bizTypeName?: string;
    /** 承租方品牌 */
    lesseeBrand?: string;
    /** 经营品类 */
    businessCategory?: string;
    /** 开业日期 */
    openDate?: string;
    /** 生产火灾危险性类别,0-丙类,1-丁类,2-戊类 */
    fireRiskCategory?: number;
    /** 喷淋系统,0-安装,1-未安装 */
    sprinklerSystem?: number;
    /** 厂房从事 */
    factoryEngaged?: string;
    /** 交接日期 */
    deliverDate?: string;
    /** 车位类型,0-人防,1-非人防 */
    parkingSpaceType?: number;
    /** 是否包含车位管理费 */
    hasParkingFee?: boolean;
    /** 车位管理费金额 */
    parkingFeeAmount?: number;
    /** 补充条款 */
    otherInfo?: string;
    /** 合同附件 */
    contractAttachments?: string;
    /** 附件-平面图 */
    attachmentsPlan?: string;
    /** 是否上传签署文件 0-否,1-是 */
    isUploadSignature?: boolean;
    /** 是否确认签署 0-否,1-是 */
    isSignatureConfirm?: boolean;
    /** 是否确认纸质文件 0-否,1-是 */
    isPaperConfirm?: boolean;
    /** 合同是否完全结束 0-否,1-是 */
    isFinish?: boolean;
    /** 创建时间 */
    createTime?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 0-否,1-是 */
    isDel?: boolean;
    /** 合同客户信息 */
    customer?: ContractCustomerDTO;
    /** 合同定单列表 */
    bookings?: ContractBookingDTO[];
    /** 合同费用列表 */
    fees?: ContractFeeDTO[];
    /** 合同应收列表 */
    costs?: ContractCostDTO[];
    /** 合同房源列表 */
    rooms?: ContractRoomDTO[];
    /** 合同签署附件 */
    signAttachments?: string;
    changeType?: string;
    dailyActivityStartTime?: string;
    dailyActivityEndTime?: string;
    /** 物业费支付周期 */
    pmPayPeriod?: number;
    specialChangeId?: string;
    specialChangeType?: number;
}

interface ContractAddResult {
    contractId: string;
    contractNo: string;
}

/**
 * 保存合同信息
 * @param params 合同信息
 * @returns
 */
export function saveContract(params: ContractAddDTO) {
    return http.post<ContractAddResult>(
        '/business-rent-admin/contract/save',
        params
    );
}

/**
 * 非标合同应收计划计算金额
 * @param params 合同信息
 * @returns
 */
export function contractCalculateMoney(params: ContractAddDTO) {
    return http.post<ContractCostPlanVo>(
        '/business-rent-admin/contract/calculateMoney',
        params
    );
}

/**
 * 客户下拉列表查询
 * @param params 查询参数
 * @returns
 */
export function getCustomerOptions(params: CustomerOptionsDTO) {
    return http.post<CustomerVo[]>(
        '/business-rent-admin/contract/customerOptions',
        params
    );
}

/**
 * 合同查询接口
 * @param id 合同ID
 * @returns
 */
export function getContractById(id: string) {
    return http.get<ContractVo>('/business-rent-admin/contract/getById', {
        id,
    });
}

/**
 * 合同权限信息
 */
export interface ContractPermissionVo {
    /** 是否可选非标合同 */
    canSelectNonStandard: boolean;
    /** 是否不受合同签订时间固化限制 */
    notRestrictedBySignTime: boolean;
    /** 固化天数 */
    solidificationDays: number;
}

/**
 * 合同应收计划视图对象
 */
export interface ContractCostPlanVo {
    /** 应收计划列表 */
    costs?: ContractCostDTO[];
    /** 定单列表 */
    bookings?: ContractBookingDTO[];
}

/**
 * 作废合同
 * @param id 合同ID
 * @returns
 */
export function invalidContract(id: string) {
    return http.post<boolean>(
        '/business-rent-admin/contract/invalid',
        undefined,
        { params: { id } }
    );
}

/**
 * 生成应收计划
 * @param params 合同信息
 * @returns
 */
export function generateCost(params: ContractAddDTO) {
    return http.post<ContractCostPlanVo>(
        '/business-rent-admin/contract/generateCost',
        params
    );
}

/**
 * 获取用户相关权限
 * @returns
 */
export function getContractPermission() {
    return http.get<ContractPermissionVo>(
        '/business-rent-admin/contract/getPermission'
    );
}

/**
 * 删除合同
 * @param id 合同ID
 * @returns
 */
export function deleteContract(id: string) {
    return http.delete<boolean>('/business-rent-admin/contract/delete', { id });
}

/**
 * 合同上传签署附件DTO
 */
export interface ContractUploadSignAttachmentDTO {
    /** 合同ID */
    contractId: string;
    /** 附件列表 */
    attachments: Array<{
        /** 文件名 */
        fileName: string;
        /** 文件地址 */
        fileUrl: string;
        /** 是否确认：0-待确认，1-待确收，2-已确收 */
        status: number;
    }>;
}

/**
 * 合同查询DTO
 */
export interface ContractQueryDTO {
    /** 分页参数 */
    params?: Record<string, any>;
    /** 页码 */
    pageNum?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 主键ID */
    id?: string;
    /** 合同号 */
    contractNo?: string;
    /** 统一id */
    unionId?: string;
    /** 版本号 */
    version?: string;
    /** 是否当前生效版本 */
    isCurrent?: boolean;
    /** 一级状态 */
    status?: number;
    /** 二级状态 */
    statusTwo?: number;
    /** 审核状态 */
    approveStatus?: number;
    /** 最新操作类型 */
    operateType?: number;
    /** 合同类型 */
    contractType?: number;
    /** 我方签约主体信息 */
    ourSigningParty?: string;
    /** 签约方式 */
    signWay?: number;
    /** 签约类型 */
    signType?: number;
    /** 续签源合同ID */
    originId?: string;
    /** 用地性质 */
    landUsage?: string;
    /** 合同签约人id */
    signerId?: string;
    /** 签约人姓名 */
    signerName?: string;
    /** 责任人id */
    ownerId?: string;
    /** 责任人姓名 */
    ownerName?: string;
    /** 合同模式 */
    contractMode?: number;
    /** 纸质合同号 */
    paperContractNo?: string;
    /** 签订日期 */
    signDate?: string;
    /** 交房日期 */
    handoverDate?: string;
    /** 合同用途 */
    contractPurpose?: number;
    /** 成交渠道 */
    dealChannel?: number;
    /** 协助人id */
    assistantId?: string;
    /** 协助人姓名 */
    assistantName?: string;
    /** 租赁期限-年 */
    rentYear?: number;
    /** 租赁期限-月 */
    rentMonth?: number;
    /** 租赁期限-日 */
    rentDay?: number;
    /** 开始日期 */
    startDate?: string;
    /** 结束日期 */
    endDate?: string;
    /** 实际生效日期 */
    effectDate?: string;
    /** 定单关联方式 */
    bookingRelType?: number;
    /** 保证金应收日期 */
    bondReceivableDate?: string;
    /** 保证金应收日期类型 */
    bondReceivableType?: number;
    /** 保证金金额类型 */
    bondPriceType?: number;
    /** 保证金金额 */
    bondPrice?: number;
    /** 收费方式 */
    chargeWay?: number;
    /** 租金应收日期 */
    rentReceivableDate?: string;
    /** 租金应收日期类型 */
    rentReceivableType?: number;
    /** 租金账单周期 */
    rentTicketPeriod?: number;
    /** 租金支付周期 */
    rentPayPeriod?: number;
    /** 递增间隔(年) */
    increaseGap?: number;
    /** 递增率 */
    increaseRate?: number;
    /** 租金递增规则(非标) */
    increaseRule?: string;
    /** 预估营收额 */
    estimateRevenue?: number;
    /** 抽点类型 */
    revenueType?: number;
    /** 是否包含物业费 */
    isIncludePm?: boolean;
    /** 月物业费单价 */
    pmUnitPrice?: number;
    /** 月物业费总价 */
    pmMonthlyPrice?: number;
    /** 合同总价 */
    totalPrice?: number;
    /** 业态id */
    bizTypeId?: string;
    /** 业态 */
    bizTypeName?: string;
    /** 承租方品牌 */
    lesseeBrand?: string;
    /** 经营品类 */
    businessCategory?: string;
    /** 开业日期 */
    openDate?: string;
    /** 生产火灾危险性类别 */
    fireRiskCategory?: number;
    /** 喷淋系统 */
    sprinklerSystem?: number;
    /** 厂房从事 */
    factoryEngaged?: string;
    /** 交接日期 */
    deliverDate?: string;
    /** 车位类型 */
    parkingSpaceType?: number;
    /** 是否包含车位管理费 */
    hasParkingFee?: boolean;
    /** 车位管理费金额 */
    parkingFeeAmount?: number;
    /** 附件-平面图 */
    attachmentsPlan?: string;
    /** 是否上传签署文件 */
    isUploadSignature?: boolean;
    /** 是否确认签署 */
    isSignatureConfirm?: boolean;
    /** 是否确认纸质文件 */
    isPaperConfirm?: boolean;
    /** 合同是否完全结束 */
    isFinish?: boolean;
    /** 创建人 */
    createBy?: string;
    /** 创建人姓名 */
    createByName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新人 */
    updateBy?: string;
    /** 更新人姓名 */
    updateByName?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 是否删除 */
    isDel?: boolean;
    /** 项目id */
    projectId?: string;
    /** 房源名称 */
    roomName?: string;
    /** 客户名称 */
    customerName?: string;
    /** 操作类型列表 */
    operateTypes?: number[];
    /** 状态列表 */
    statuses?: number[];
    /** 二级状态列表 */
    statusTwos?: number[];
    /** 签订日期开始 */
    signDateStart?: string;
    /** 签订日期结束 */
    signDateEnd?: string;
    /** 合同用途列表 */
    contractPurposes?: number[];
    /** 签约方式列表 */
    signWays?: number[];
}

/**
 * 表格数据信息
 */
export interface TableDataInfo<T = any> {
    /** 总记录数 */
    total: number;
    /** 列表数据 */
    rows: T[];
    /** 状态码 */
    code: number;
    /** 返回消息 */
    msg: string;
}

/**
 * 合同补充协议VO
 */
export interface ContractSupplementVo {
    /** 合同id */
    contractId: string;
    /** 补充协议类型 */
    supplementType: string;
    /** 签订日期 */
    signDate: string;
    /** 实际生效日期 */
    effectDate: string;
    /** 一级状态 */
    status: number;
    /** 审核状态 */
    approveStatus: number;
}

/**
 * 合同修改记录VO
 */
export interface ContractModifyLogVo {
    /** 主键ID */
    id: string;
    /** 合同id */
    contractId: string;
    /** 合同统一id */
    unionId: string;
    /** 修改类型 */
    modifyType: number;
    /** 旧值 */
    oldValue: string;
    /** 新值 */
    newValue: string;
    /** 创建人姓名 */
    createByName: string;
    /** 更新人姓名 */
    updateByName: string;
    /** 是否删除 */
    isDel: boolean;
}

/**
 * 账单流水关系VO
 */
export interface CostFlowRelVo {
    /** 主键ID */
    id: string;
    /** 账单id */
    costId: string;
    /** 退款单id */
    refundId: string;
    /** 流水id */
    flowId: string;
    /** 流水单号 */
    flowNo: string;
    /** 账单类型 */
    type: number;
    /** 确认状态 */
    confirmStatus: number;
    /** 确认时间 */
    confirmTime: string;
    /** 确认人id */
    confirmUserId: string;
    /** 确认人姓名 */
    confirmUserName: string;
    /** 支付金额 */
    payAmount: number;
    /** 本次记账金额 */
    acctAmount: number;
    /** 创建人姓名 */
    createByName: string;
    /** 创建时间 */
    createTime: string;
    /** 更新人姓名 */
    updateByName: string;
    /** 是否删除 */
    isDel: boolean;
    /** 项目id */
    projectId: string;
    /** 项目名称 */
    projectName: string;
    /** 入账时间 */
    entryTime: string;
    /** 支付类型 */
    payType: number;
    /** 支付方式 */
    payMethod: number;
    /** 订单号 */
    orderNo: string;
    /** 已使用金额 */
    usedAmount: number;
    /** 支付人姓名 */
    payerName: string;
    /** 支付对象 */
    target: string;
    /** 收款商户 */
    merchant: string;
}

/**
 * 上传签署附件
 * @param params 附件信息
 * @returns
 */
export function uploadSignAttachment(params: ContractUploadSignAttachmentDTO) {
    return http.post<boolean>(
        '/business-rent-admin/contract/uploadSignAttachment',
        params
    );
}

/**
 * 更新合同信息
 * @param params 合同信息
 * @param modifyType 修改类型
 * @returns
 */
export function modifyContract(params: ContractAddDTO, modifyType: number) {
    return http.post<boolean>('/business-rent-admin/contract/modify', params, {
        params: { modifyType },
    });
}

/**
 * 查询合同信息列表
 * @param params 查询参数
 * @returns
 */
export function getContractList(params: ContractQueryDTO) {
    return http.post<TableDataInfo<ContractVo>>(
        '/business-rent-admin/contract/list',
        params
    );
}

/**
 * 确认签署附件
 * @param params 附件信息
 * @returns
 */
export function confirmSignAttachment(params: ContractUploadSignAttachmentDTO) {
    return http.post<boolean>(
        '/business-rent-admin/contract/confirmSignAttachment',
        params
    );
}

/**
 * 查询版本列表
 * @param id 合同ID
 * @returns
 */
export function getContractVersionList(id: string) {
    return http.get<ContractVo[]>(
        '/business-rent-admin/contract/version/list',
        { id }
    );
}

/**
 * 查询补充协议列表
 * @param id 合同ID
 * @returns
 */
export function getSupplementList(id: string) {
    return http.get<ContractSupplementVo[]>(
        '/business-rent-admin/contract/supplement/list',
        { id }
    );
}

/**
 * 查询修改记录
 * @param id 合同ID
 * @returns
 */
export function getModifyLog(id: string) {
    return http.get<ContractModifyLogVo[]>(
        '/business-rent-admin/contract/modifyLog',
        { id }
    );
}

/**
 * 查询合同详情流水信息
 * @param id 合同ID
 * @returns
 */
export function getContractDetailFlow(id: string) {
    return http.get<CostFlowRelVo[]>(
        '/business-rent-admin/contract/detail/flow',
        { id }
    );
}

/**
 * 查询合同详情页账单
 * @param id 合同ID
 * @returns
 */
export function getContractDetailBill(id: string) {
    return http.get<ContractVo>('/business-rent-admin/contract/detail/bill', {
        id,
    });
}

/**
 * 批量更新合同责任人DTO
 */
export interface ContractUpdateOwnerDTO {
    /** 合同id列表 */
    contractIds: string[];
    /** 责任人id */
    ownerId: string;
    /** 责任人姓名 */
    ownerName: string;
}

/**
 * 合同二级状态数量统计VO
 */
export interface ContractSummaryVo {
    /** 合同期未开始数量 */
    contractPeriodNotStarted?: number;
    /** 保证金未收齐数量 */
    bondNotFullyReceived?: number;
    /** 合同期未开始&保证金未收齐数量 */
    periodNotStartedAndBondNotReceived?: number;
    /** 正常退租数量 */
    normalTermination?: number;
    /** 提前退租数量 */
    earlyTermination?: number;
    /** 换房数量 */
    roomChange?: number;
    /** 续签数量 */
    renewal?: number;
    /** 正常退租待出场结算数量 */
    normalTerminationPendingCheckout?: number;
    /** 提前退租待出场结算数量 */
    earlyTerminationPendingCheckout?: number;
    /** 待退租数量 */
    pendingTermination?: number;
    /** 作废数量 */
    invalid?: number;
    /** 作废重签数量 */
    invalidRenewal?: number;
}

/**
 * 批量更新合同责任人
 * @param params 更新参数
 * @returns
 */
export function updateContractOwner(params: ContractUpdateOwnerDTO) {
    return http.post<boolean>(
        '/business-rent-admin/contract/updateOwner',
        params
    );
}

/**
 * 合同二级状态数量统计
 * @param params 查询参数
 * @returns
 */
export function getContractSummary(params: ContractQueryDTO) {
    return http.post<ContractSummaryVo>(
        '/business-rent-admin/contract/summary',
        params
    );
}

/**
 * 合同变更保存接口
 * @param params 合同变更信息
 * @returns
 */
export function saveContractChange(params: ContractAddDTO) {
    return http.post<ContractAddResult>(
        '/business-rent-admin/contract/saveChange',
        params
    );
}




/**
 * 合同模板接口
 */
export interface ContractTemplateVo {
    /** 模板ID */
    id: string;
    /** 模板名称 */
    templateName: string;
    /** 应用级别 */
    applyLevel: number;
    /** 应用范围 */
    applyScope: string | null;
    /** 合同用途 */
    contractPurpose: number;
    /** 创建人姓名 */
    createdByName: string;
    /** 创建时间 */
    createTime: string;
    /** 生效日期 */
    effectiveDate: string;
    /** 过期日期 */
    expirationDate: string | null;
    /** 链接地址 */
    linkUrl: string | null;
    /** 打印类型 */
    printType: number;
    /** 项目ID列表 */
    projectIdList: string | null;
    /** 备注 */
    remark: string | null;
    /** 状态 */
    status: number;
    /** 消息 */
    msg: string;
}

/**
 * 查询合同套打模版
 * @param id 合同ID
 * @returns
 */
export function getContractTemplateList(contractId: string) {
    return http.get<ContractTemplateVo[]>(
        '/business-rent-admin/contract/templateList',
        { contractId }
    );
}

export interface PrintVo {
   fileName:string
   fileUrl:string
}

/**
 * 合同套打
 * @param id 合同ID
 * @returns
 */
export function print(contractId: string,templateId: string ) {
    return http.get<PrintVo>(
        '/business-rent-admin/contract/print',
        { contractId,templateId }
    );
}

/**
 * 下载合同导入模板
 * @returns Promise<ResponseData<Blob>>
 */
export function downloadContractTemplate(){
    return http.downloadForGet('/business-rent-admin/contract/template/download')
  }

/**
 * 通过模版导入合同
 * @param file 文件数据
 * @returns Promise<ResponseData<AjaxResult>>
 */
export function importContractTemplate(file: File){
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/business-rent-admin/contract/template/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }  