<template>
    <div class="contract-reduction">
        <section>
            <sectionTitle title="临时收费信息"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <!-- 表格区域 -->
            <a-table
                :columns="columns"
                :data="tableData"
                :bordered="{ cell: true }"
                :scroll="{ x: 1 }"
                :stripe="true"
                :loading="loading"
                row-key="id"
            >
                <template #costType="{ record }">
                    <a-tag color="blue">{{
                        getCostTypeText(record.costType)
                    }}</a-tag>
                </template>
                <template #approveStatus="{ record }">
                    <a-tag :color="getApproveStatusColor(record.approveStatus)">
                        {{ getApproveStatusText(record.approveStatus) }}
                    </a-tag>
                </template>
                <template #totalAmount="{ record }">
                    {{ formatAmount(record.totalAmount) }}
                </template>
                <template #operations="{ record }">
                    <a-space>
                        <a-button
                            type="text"
                            size="mini"
                            @click="handleView(record)"
                        >
                            查看
                        </a-button>
                        <a-button
                            type="text"
                            size="mini"
                            @click="handleEdit(record)"
                            v-if="record.approveStatus === 0"
                        >
                            编辑
                        </a-button>
                        <a-button
                            type="text"
                            size="mini"
                            @click="handleSubmit(record)"
                            v-if="record.approveStatus === 0"
                        >
                            提交审批
                        </a-button>
                        <a-button
                            type="text"
                            size="mini"
                            @click="handleApprove(record)"
                            v-if="record.approveStatus === 1"
                        >
                            审批
                        </a-button>
                        <a-button
                            type="text"
                            size="mini"
                            @click="handleDelete(record)"
                            v-if="record.approveStatus === 0"
                        >
                            删除
                        </a-button>
                    </a-space>
                </template>
            </a-table>
        </a-card>
        <!-- 新增/编辑临时收费抽屉 -->
        <temporary-cost-drawer
            v-if="showDrawer"
            ref="temporaryCostDrawerRef"
            @success="handleDrawerSuccess"
            @cancel="handleDrawerCancel"
        />
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';
    import { Message, Modal } from '@arco-design/web-vue';
    import {
        getReductionList,
        deleteReduction,
        exportReductionList,
        type ReductionQueryDTO,
    } from '@/api/reduction';
    import {
        getTemporaryCostList,
        deleteTemporaryCost,
        submitTemporaryCost,
        approveTemporaryCost,
        exportTemporaryCostList,
        type TemporaryCostQueryDTO,
        type TemporaryCostVo,
        type TemporaryCostExportDTO,
        TemporaryApproveStatus,
    } from '@/api/temporaryCost';
    import TemporaryCostDrawer from '@/views/temporaryCharges/components/TemporaryCostDrawer.vue';
    import { DictType, getDictLabel } from '@/dict/index';
    import { dictData } from '@/dict/data';

    const tableData = ref<TemporaryCostVo[]>([]);
    const loading = ref(false);

    // 表格列配置
    const columns = [
        {
            title: '序号',
            slotName: 'index',
            width: 80,
            align: 'center',
        },
        {
            title: '项目',
            dataIndex: 'projectName',
            ellipsis: true,
            tooltip: true,
            align: 'center',
            width: 150,
        },
        {
            title: '费用类型',
            dataIndex: 'costType',
            slotName: 'costType',
            align: 'center',
            width: 100,
        },
        {
            title: '收费用途',
            dataIndex: 'subjectName',
            ellipsis: true,
            tooltip: true,
            align: 'center',
            width: 120,
        },
        {
            title: '合同号',
            dataIndex: 'contractNo',
            ellipsis: true,
            tooltip: true,
            align: 'center',
            width: 150,
        },
        {
            title: '承租人',
            dataIndex: 'customerName',
            ellipsis: true,
            tooltip: true,
            align: 'center',
            width: 120,
        },
        {
            title: '租赁单元',
            dataIndex: 'roomName',
            ellipsis: true,
            tooltip: true,
            align: 'center',
            width: 150,
        },
        {
            title: '账单周期',
            dataIndex: 'costPeriod',
            align: 'center',
            width: 120,
        },
        {
            title: '应收日期',
            dataIndex: 'receivableDate',
            align: 'center',
            width: 120,
        },
        {
            title: '账单总额',
            dataIndex: 'totalAmount',
            slotName: 'totalAmount',
            align: 'center',
            width: 120,
        },
        {
            title: '审批状态',
            dataIndex: 'approveStatus',
            slotName: 'approveStatus',
            align: 'center',
            width: 100,
        },
        {
            title: '创建人',
            dataIndex: 'createByName',
            align: 'center',
            width: 100,
        },
        {
            title: '创建日期',
            dataIndex: 'createTime',
            align: 'center',
            width: 160,
        },
        {
            title: '操作',
            slotName: 'operations',
            width: 200,
            ellipsis: false,
            tooltip: false,
            fixed: 'right',
            align: 'center',
        },
    ];
    // 抽屉相关
    const temporaryCostDrawerRef = ref();
    const showDrawer = ref(false);

    // 费用类型选项（排除定金、保证金、租金）
    const costTypeOptions = computed(() => {
        const allCostTypes = dictData[DictType.COST_TYPE_AND_TAX_RATE] || [];
        // 排除定金(10)、保证金(20)、租金(30)
        return allCostTypes.filter(
            (item) => ![10, 20, 30].includes(item.value)
        );
    });
    // 查询数据
    const search = async () => {
        try {
            loading.value = true;
            const params: TemporaryCostQueryDTO = {
                pageNum: 1,
                pageSize: 10,
                // projectId: filterForm.projectId,
                // contractNo: filterForm.contractNo || undefined,
                // roomName: filterForm.roomName || undefined,
                // customerName: filterForm.customerName || undefined,
                // costType: filterForm.costType,
                // approveStatus: filterForm.approveStatus,
            };

            const response = await getTemporaryCostList(params);
            if (response) {
                tableData.value = response.rows || [];
            }
        } catch (error) {
            console.error('获取临时收费列表失败:', error);
            tableData.value = [];
        } finally {
            loading.value = false;
        }
    };

    // 查看
    const handleView = async (record: TemporaryCostVo) => {
        showDrawer.value = true;
        await nextTick();
        temporaryCostDrawerRef.value?.show('view', record);
    };

    // 编辑
    const handleEdit = async (record: TemporaryCostVo) => {
        showDrawer.value = true;
        await nextTick();
        temporaryCostDrawerRef.value?.show('edit', record);
    };

    // 删除
    const handleDelete = (record: TemporaryCostVo) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这条临时收费记录吗？',
            onOk: async () => {
                try {
                    await deleteTemporaryCost(record.id!);
                    Message.success('删除成功');
                    search();
                } catch (error) {
                    console.error('删除失败:', error);
                }
            },
        });
    };

    // 提交审批
    const handleSubmit = (record: TemporaryCostVo) => {
        Modal.confirm({
            title: '确认提交',
            content: '确定要提交这条临时收费记录进行审批吗？',
            onOk: async () => {
                try {
                    await submitTemporaryCost(record.id!);
                    Message.success('提交成功');
                    search();
                } catch (error) {
                    console.error('提交失败:', error);
                }
            },
        });
    };

    // 审批通过
    const handleApprove = (record: TemporaryCostVo) => {
        Modal.confirm({
            title: '确认审批',
            content: '确定要审批通过这条临时收费记录吗？',
            onOk: async () => {
                try {
                    await approveTemporaryCost(record.id!);
                    Message.success('审批成功');
                    search();
                } catch (error) {
                    console.error('审批失败:', error);
                }
            },
        });
    };
    // 抽屉成功回调
    const handleDrawerSuccess = () => {
        showDrawer.value = false;
        search();
    };

    // 抽屉取消回调
    const handleDrawerCancel = () => {
        showDrawer.value = false;
    };
    // 审批状态文本
    const getApproveStatusText = (status: number | undefined) => {
        const statusMap: Record<number, string> = {
            0: '草稿',
            1: '审批中',
            2: '已通过',
            3: '已驳回',
            4: '作废',
        };
        return statusMap[status || 0] || '-';
    };

    // 审批状态颜色
    const getApproveStatusColor = (status: number | undefined) => {
        const colorMap: Record<number, string> = {
            0: 'gray',
            1: 'blue',
            2: 'green',
            3: 'red',
            4: 'red',
        };
        return colorMap[status || 0] || 'gray';
    };

    // 金额格式化
    const formatAmount = (amount: number | undefined) => {
        if (amount === undefined || amount === null) return '';
        return amount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
    };

    // 获取费用类型文本
    const getCostTypeText = (costType: number | undefined) => {
        if (costType === undefined || costType === null) return '-';
        return getDictLabel(DictType.COST_TYPE_AND_TAX_RATE, costType) || '-';
    };
</script>
<style scoped lang="less"></style>
