<template>
    <div class="data-roles-list">
        <div class="common-flex data-roles-list-header">
            <div class="data-roles-list-header-title">
                <span class="common-header-tag"></span>
                <span>功能角色列表</span>
            </div>
            <div class="common-flex data-roles-list-header-right">
                <a-button v-permission="['system:menu:role:add']" type="text" @click="handleAdd" size="small" class="data-roles-list-header-right-button">
                    <icon-plus-circle />
                    添加
                </a-button>
            </div>
        </div>
        <div class="data-roles-list-search">
            <a-input v-model:value="searchValue" placeholder="请输入内容" />
        </div>
        <div class="data-roles-list-content">
            <div class="data-roles-list-content-list">
                <div v-for="item in list" :key="item.roleId" class="common-flex data-roles-list-content-item" :class="{ 'data-roles-list-content-item-on': currentItemId === item.roleId }" @click="handleItem(item)">
                    <div class="data-roles-list-content-item-left">{{ item.roleName }}</div>
                    <div class="data-roles-list-content-item-right">
                        <div class="data-roles-list-content-item-right-handle">
                            <icon-edit v-permission="['system:menu:role:edit']" @click.stop="handleEdit(item.roleId)" />
                            <icon-delete v-permission="['system:menu:role:remove']" @click.stop="handleDelete(item.roleId)" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 添加数据角色 角色名称 备注 -->
        <a-modal :visible="open" :title="editRole ? '编辑功能角色' : '添加功能角色'" :mask-closable="true" @ok="handleOk" @cancel="handleCancel">
            <a-form :model="formModel" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 14 }">
                <a-form-item field="roleName" label="角色名称">
                    <a-input v-model="formModel.roleName" placeholder="请输入角色名称" />
                </a-form-item>
                <a-form-item field="remark" label="角色说明">
                    <a-input v-model="formModel.remark" placeholder="请输入角色说明" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, nextTick } from 'vue'
import { getRoleList, saveRole, deleteRole, updateRole } from '@/api/role'
import { Message, Modal } from '@arco-design/web-vue';

const searchValue = ref('')
const list = ref<any[]>([])
const currentItemId = ref(null)
const open = ref(false)
const formModel = ref({
    roleName: '',
    remark: '',
    roleId: '',
})
const rules = ref({
    roleName: [{ required: true, message: '请输入角色名称' }],
    remark: [{ required: true, message: '请输入角色说明' }],
})
const editRole = ref<any>(null)
const emit = defineEmits(['change'])
const handleEdit = (roleId: string) => {
    console.log('编辑', roleId)
    open.value = true
    editRole.value = list.value.find((item: any) => item.roleId === roleId)
    nextTick(() => {
        formModel.value = {
            roleName: editRole.value?.roleName || '',
            remark: editRole.value?.remark || '',
            roleId: editRole.value?.roleId || '',
        }
    })
}
const handleDelete = (roleId: string) => {
    console.log('删除', roleId)
    Modal.confirm({
        title: '删除功能角色',
        content: '确定删除该功能角色吗？',
        onOk: () => {
            console.log('删除', roleId)
            deleteRole({ roleId }).then((res: any) => {
                if (res.code === 200) {
                    Message.success('删除成功')
                    fetchData()
                } else {
                    Message.error(res.msg)
                }
            })
        },
    })
}
const handleAdd = () => {
    console.log('添加')
    formModel.value = {
        roleName: '',
        remark: '',
        roleId: '',
    }
    open.value = true
}
const handleOk = () => {
    console.log(formModel.value)
    if (formModel.value.roleName === '') {
        Message.error('请输入角色名称')
        return
    }
    if (formModel.value.remark === '') {
        Message.error('请输入角色说明')
        return
    }
    if (editRole.value) {
        updateRole({...formModel.value, type: 2}).then((res: any) => {
            if (res.code === 200) {
                Message.success('编辑成功')
                open.value = false
                fetchData()
            } else {
                Message.error(res.msg)
            }
        }).catch((err) => {
            Message.error(err.message)
        })
    } else {
        saveRole({...formModel.value, type: 2}).then((res: any) => {
            if (res.code === 200) {
                Message.success('添加成功')
                open.value = false
                fetchData()
            } else {
                Message.error(res.msg)
            }
        }).catch((err) => {
            Message.error(err.message)
        })
    }
}
const handleCancel = () => {
    open.value = false
    editRole.value = null
}
const handleItem = (item: any) => {
    currentItemId.value = item.roleId
    emit('change', item)
}

const fetchData = async () => {
    const { data } = await getRoleList({ type: 2 })
    list.value = data as any[]
}

fetchData()
</script>
<script lang="ts">
    export default { name: 'functionRolesList' }
</script>

<style lang="less" scoped>
    .data-roles-list {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 4px;
        padding: 12px;
        box-sizing: border-box;
        .data-roles-list-header {
            justify-content: space-between;
            .data-roles-list-header-title {
                display: flex;
                align-items: center;
            }
            .data-roles-list-header-right {
                display: flex;
                align-items: center;
                color: rgb(var(--arcoblue-6));
                cursor: pointer;
                .data-roles-list-header-right-button {
                    padding: 0 !important;
                }
            }
        }
        .data-roles-list-search {
            margin-top: 12px;
        }
        .data-roles-list-content {
            margin-top: 12px;

            .data-roles-list-content-list {
                .data-roles-list-content-item {
                    padding: 12px;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    justify-content: space-between;
                    &:hover {
                        background: var(--color-fill-2);
                    }
                    .data-roles-list-content-item-right-handle {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        cursor: pointer;
                    }
                }
                .data-roles-list-content-item-on {
                    background: rgb(var(--arcoblue-6));
                    color: #fff;
                    &:hover {
                        background: rgb(var(--arcoblue-6));
                    }
                }
            }
        }
    }
</style>
