<template>
    <div class="contract-agreement">
        <section>
            <sectionTitle title="补充协议信息">
            </sectionTitle>
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <a-table :columns="columns" :data="tableData" :pagination="false" :scroll="{ x: 1000 }"
                    :bordered="{ cell: true }" :loading="loading">
                    <template #supplementType="{ record }">
                        {{ record.supplementType || '-' }}
                    </template>
                    <template #signDate="{ record }">
                        {{ record.signDate ? dayjs(record.signDate).format('YYYY-MM-DD') : '-' }}
                    </template>
                    <template #effectDate="{ record }">
                        {{ record.effectDate ? dayjs(record.effectDate).format('YYYY-MM-DD') : '-' }}
                    </template>
                    <template #status="{ record }">
                        <a-tag :color="getStatusColor(record.status)">
                            {{ getStatusText(record.status) }}
                        </a-tag>
                    </template>
                    <template #approveStatus="{ record }">
                        <a-tag :color="getApproveStatusColor(record.approveStatus)">
                            {{ getApproveStatusText(record.approveStatus) }}
                        </a-tag>
                    </template>
                    <template #action="{ record }">
                        <a-button type="text" size="small" @click="handleView(record)">查看</a-button>
                        <a-button type="text" size="small" v-if="record.approveStatus < 2"
                            @click="handleEdit(record)">编辑</a-button>
                        <a-button type="text" size="small" v-if="record.approveStatus < 2"
                            @click="handleDelete(record)">删除</a-button>
                    </template>
                </a-table>
            </a-card>
        </section>

        <!-- 扩租弹窗 -->
        <expandContractDrawer ref="expandContractRef" @submit="handleChange" />
        <!-- 换房弹窗 -->
        <swapContractDrawer ref="swapContractRef" @submit="handleChange" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import sectionTitle from '@/components/sectionTitle/index.vue';
import { getSupplementList } from '@/api/contract';
import { useContractStore } from '@/store/modules/contract';
import { ContractAddDTO } from '@/api/contract';
import dayjs from 'dayjs';

import expandContractDrawer from '@/views/contract/expandContractDrawer.vue';
import swapContractDrawer from '@/views/contract/swapContractDrawer.vue';
import { deleteContractChange } from '@/api/contractChange';
import { Message, Modal, type TableColumnData } from '@arco-design/web-vue'
import { IconExclamationCircleFill } from '@arco-design/web-vue/es/icon'

const contractStore = useContractStore();
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO);

const tableData = ref<any[]>([]);
const loading = ref(false);

// 扩租和换房弹窗引用
const expandContractRef = ref();
const swapContractRef = ref();

const columns = computed(() => {
    return [
        {
            title: '补充协议类型',
            dataIndex: 'supplementType',
            slotName: 'supplementType',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '签订日期',
            dataIndex: 'signDate',
            slotName: 'signDate',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '生效日期',
            dataIndex: 'effectDate',
            slotName: 'effectDate',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '单据状态',
            dataIndex: 'status',
            slotName: 'status',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '审批状态',
            dataIndex: 'approveStatus',
            slotName: 'approveStatus',
            width: 120,
            align: 'center',
            ellipsis: true,
            tooltip: true,
        },
        {
            title: '操作',
            dataIndex: 'action',
            slotName: 'action',
            width: 180,
            align: 'center',
            fixed: 'right',
        }
    ];
});

// 获取补充协议列表
const fetchSupplementList = async () => {
    if (!contractData.value.id) {
        console.warn('合同ID不存在，无法获取补充协议列表');
        return;
    }

    loading.value = true;
    try {
        const response = await getSupplementList(contractData.value.id);
        if (response && response.data) {
            tableData.value = Array.isArray(response.data) ? response.data : [];
        } else if (Array.isArray(response)) {
            // 如果响应直接是数组
            tableData.value = response;
        } else {
            tableData.value = [];
        }
    } catch (error) {
        console.error('获取补充协议列表失败:', error);
        tableData.value = [];
    } finally {
        loading.value = false;
    }
};

// 获取单据状态文本
const getStatusText = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        10: '草稿',
        20: '待生效',
        30: '生效中',
        40: '失效',
        50: '作废'
    };
    return statusMap[status || 0] || '-';
};

// 获取单据状态颜色
const getStatusColor = (status: number | undefined) => {
    const colorMap: Record<number, string> = {
        10: 'gray',
        20: 'orange',
        30: 'green',
        40: 'red',
        50: 'red'
    };
    return colorMap[status || 0] || 'gray';
};

// 获取审批状态文本
const getApproveStatusText = (approveStatus: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '待审核',
        1: '审核中',
        2: '审核通过',
        3: '审核拒绝'
    };
    return statusMap[approveStatus || 0] || '-';
};

// 获取审批状态颜色
const getApproveStatusColor = (approveStatus: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'gray',
        1: 'blue',
        2: 'green',
        3: 'red'
    };
    return colorMap[approveStatus || 0] || 'gray';
};

// 查看
const handleView = (record: any) => {
    console.log('查看补充协议:', record);
    openContractDrawer(record, 'view');
};

// 编辑
const handleEdit = (record: any) => {
    console.log('编辑补充协议:', record);
    openContractDrawer(record, 'edit');
};

// 删除
const handleDelete = (record: any) => {
    console.log('删除补充协议:', record);
    // TODO: 实现删除功能
    Modal.info({
        title: '提示',
        content: () => {
            return h('div', {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }
            }, [h(IconExclamationCircleFill, {
                style: {
                    color: 'rgb(var(--warning-6))',
                    fontSize: '22px'
                }
            }), '是否确认执行当前删除操作？'])
        },
        okText: '确认',
        cancelText: '取消',
        hideCancel: false,
        alignCenter: false,
        simple: false,
        maskClosable: false,
        modalClass: 'confirm-popup',
        width: 400,
        onOk: async () => {
            const loading = Message.loading('删除中...')
            try {
                let id = ''
                if (record.supplementType === '合同扩租' || record.supplementType === '合同换房') {
                    id = record.changeId
                } else if (record.supplementType === '合同缩租' || record.supplementType === '合同退租') {
                    id = record.terminateId
                }
                await deleteContractChange(id)
                loading.close()
                Message.success('删除成功')
                // 重新加载数据
                fetchSupplementList()
            } catch (error: any) {
                loading.close()
                console.error('删除合同失败:', error)
            }
        }
    })
};

// 打开合同弹窗
const openContractDrawer = (record: any, mode: 'view' | 'edit') => {
    const supplementType = record.supplementType;

    if (supplementType === '合同扩租') {
        // 扩租：使用 changeId 获取扩租详情
        const changeId = record.changeId;
        if (expandContractRef.value) {
            // 构建扩租数据对象
            const expandData = {
                id: contractData.value.id, // 合同ID
                specialChangeId: changeId, // 扩租详情ID
                specialChangeType: 1, // 扩租类型
                mode: mode // 传递模式参数
            };
            expandContractRef.value.open(expandData);
        }
    } else if (supplementType === '合同换房') {
        // 换房：使用 terminateId 获取换房详情
        const changeId = record.changeId;
        if (swapContractRef.value) {
            // 构建换房数据对象
            const swapData = {
                id: contractData.value.id, // 合同ID
                specialChangeId: changeId, // 换房详情ID
                specialChangeType: 2, // 换房类型
                mode: mode // 传递模式参数
            };
            swapContractRef.value.open(swapData);
        }
    }
    else if (supplementType === '合同缩租' || supplementType === '合同退租') {

    } else {
        console.warn('未知的补充协议类型:', supplementType);
    }
};

// 弹窗提交回调
const handleChange = (type: number) => {
    fetchSupplementList();
};

// 监听合同数据变化，重新加载补充协议列表
watch(() => contractData.value.id, (newId) => {
    if (newId) {
        fetchSupplementList();
    }
}, { immediate: true });

// 暴露刷新方法供父组件调用
defineExpose({
    refresh: fetchSupplementList
});
</script>

<style scoped lang="less">
.contract-agreement {
    .arco-table {
        .arco-table-th {
            background-color: #fafafa;
            font-weight: 500;
        }

        .arco-table-td {
            padding: 12px 8px;
        }
    }

    .arco-tag {
        border-radius: 4px;
        font-size: 12px;
        padding: 2px 8px;
    }
}
</style>